<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminInstructorController;
use App\Http\Controllers\Admin\AdminProviderController;
use App\Http\Controllers\Admin\AdminSchoolController;
use App\Http\Controllers\Admin\AdminTodoController;
use App\Http\Controllers\AjaxController;
use App\Http\Controllers\CommonController;
use App\Http\Controllers\Parent\ParentClassController;
use App\Http\Controllers\Parent\ParentController;
use App\Http\Controllers\Parent\ParentKidController;
use App\Http\Controllers\PayPalController;
use App\Http\Controllers\Provider\ProviderClassController;
use App\Http\Controllers\Provider\ProviderController;
use App\Http\Controllers\Provider\ProviderInstructorController;
use App\Http\Controllers\School\SchoolController;
use App\Http\Controllers\School\SchoolCouponController;
use App\Http\Controllers\School\SchoolProgramController;
use App\Http\Controllers\School\SchoolServiceProviderController;
use App\Http\Controllers\School\SchoolStudentController;
use App\Http\Controllers\School\SchoolTeacherController;
use App\Http\Controllers\School\SchooolProgramScheduleController;
use App\Mail\TestMail;
use App\Models\School;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});
Route::get("/", [ParentController::class, 'signin'])->name('login');

// ******************************** Parent *************************************

Route::group(['prefix' => "parent", 'as' => 'parent.'], function () {
    Route::get("/", [ParentController::class, 'signin'])->name('login');

    Route::get("signin", [ParentController::class, 'signin'])->name('signin');
    Route::post("signin_post", [ParentController::class, 'signin_post'])->name('login.post');

    Route::get("signup", [ParentController::class, 'signup'])->name('signup');
    Route::post("signup_post", [ParentController::class, 'signup_post'])->name('signup.post');

    Route::get("reset", [ParentController::class, 'reset'])->name('reset');
    Route::post("reset_post", [ParentController::class, 'reset_post'])->name('reset.post');

    Route::get("forget", [ParentController::class, 'forget'])->name('forget');
    Route::post("forget_post", [ParentController::class, 'forget_post'])->name('forget.post');



    Route::group(['middleware' => 'auth:sanctum'], function () {
        Route::get("dashboard", [ParentController::class, 'dashboard'])->name('dashboard');

        Route::get("profile", [ParentController::class, 'profile'])->name('profile');
        Route::post("profile_update", [ParentController::class, 'profile_update'])->name('profile.update');
        Route::post("add_kid", [ParentController::class, 'add_kid'])->name('add_kid');
        Route::post("update_kid/{id}", [ParentController::class, 'update_kid'])->name('update_kid');
        Route::delete("delete_kid/{id}", [ParentController::class, 'delete_kid'])->name('delete_kid');

        Route::get("remove_profile", [ParentController::class, 'removeProfile'])->name('remove_profile');

        Route::get("cart", [ParentClassController::class, 'cart'])->name('cart');
        Route::post("add_to_cart", [ParentClassController::class, 'add_to_cart'])->name('add_to_cart');
        Route::get("remove_from_cart/{id}", [ParentClassController::class, 'remove_from_cart'])->name('remove_from_cart');
        Route::post("buy_it_now", [ParentClassController::class, 'buy_it_now'])->name('buy_it_now');
        Route::get("checkout", [ParentClassController::class, 'checkout'])->name('checkout');
        Route::post("checkout", [ParentClassController::class, 'checkout_post'])->name('checkout.post');
        Route::get('/parent/validate-coupon', [ParentClassController::class, 'validateCoupon'])->name('validate.coupon');


        Route::get("class-details/{id}", [ParentClassController::class, 'class_details'])->name('class.details');
        Route::get("enrolled-history", [ParentClassController::class, 'enrolled_history'])->name('enrolled.history');
        Route::get("enrolled-details/{id}", [ParentClassController::class, 'enrolled_details'])->name('enrolled.details');
        Route::get('/enrolled/{id}/invoice', [ParentClassController::class, 'downloadInvoice'])->name('enrolled.invoice');

        // Route::post("profile_update", [ParentController::class, 'profile_update'])->name('profile.update');

        // Route::post('/paypal/capture', [ParentClassController::class, 'capturePayment'])->name('paypal.capture');


        Route::post('/stripe/process', [ParentClassController::class, 'processPayment'])->name('stripe.process');

        Route::get("logout", [ParentController::class, 'logout'])->name('logout');

        Route::post('/parent/edit-kid-view', [ParentController::class, 'editKidView'])->name('edit_kid_view');

        Route::resource("kids", ParentKidController::class);
    });
});


// ******************************** Service Provider *************************************
Route::group(['prefix' => "service-provider", 'as' => 'service_provider.'], function () {
    Route::get("/", [ProviderController::class, 'signin'])->name('login');

    Route::get("signin", [ProviderController::class, 'signin'])->name('signin');
    Route::post("signin_post", [ProviderController::class, 'signin_post'])->name('login.post');

    Route::get("signup", [ProviderController::class, 'signup'])->name('signup');
    Route::post("signup_post", [ProviderController::class, 'signup_post'])->name('signup.post');

    Route::get("reset", [ProviderController::class, 'reset'])->name('reset');
    Route::post("reset_post", [ProviderController::class, 'reset_post'])->name('reset.post');

    Route::get("forget", [ProviderController::class, 'forget'])->name('forget');
    Route::post("forget_post", [ProviderController::class, 'forget_post'])->name('forget.post');


    Route::group(['middleware' => 'auth:service_provider'], function () {
        Route::get("dashboard", [ProviderController::class, 'dashboard'])->name('dashboard');

        Route::get("profile", [ProviderController::class, 'profile'])->name('profile');
        Route::post("profile_update", [ProviderController::class, 'profile_update'])->name('profile.update');


        Route::post("bank_update/{id}", [ProviderController::class, 'bank_update'])->name('bank.update');
        Route::post("bank_store", [ProviderController::class, 'bank_store'])->name('bank.store');
        Route::delete("bank_delete/{id}", [ProviderController::class, 'bank_delete'])->name('bank.delete');


        // instuctor
        Route::resource('instructors', ProviderInstructorController::class);
        Route::post("instructor_request_approve/{id}", [ProviderInstructorController::class, 'instructor_request_approve'])->name('instructor_request.approve');
        Route::post('/instructors/{id}/upload-document', [ProviderInstructorController::class, 'uploadDocument'])->name('instructors.upload-document');
        Route::post("upload_badge", [ProviderInstructorController::class, 'uploadBadge'])->name('upload_badge');

        Route::put("lesson_update/{id}", [ProviderClassController::class, 'lesson_update'])->name('lesson.update');
        Route::post("lesson_store", [ProviderClassController::class, 'lesson_store'])->name('lesson.store');
        Route::delete("lesson_destory/{id}", [ProviderClassController::class, 'lesson_destory'])->name('lesson.destory');

        Route::post("add_kid", [ProviderController::class, 'add_kid'])->name('add_kid');

        Route::get("remove_profile", [ProviderController::class, 'removeProfile'])->name('remove_profile');

        Route::get("class-details/{id}", [ProviderClassController::class, 'class_details'])->name('class.details');


        Route::get("current-class-details/{id}", [ProviderClassController::class, 'current_class_details'])->name('current.class.details');
        Route::get("program-attendance/{id}", [ProviderClassController::class, 'program_attendance'])->name('program.attendance');

        // bulk checkin
        Route::post("bulk_checkin", [ProviderClassController::class, 'markAttendance'])->name('bulk_checkin');
        Route::post("checkout", [ProviderClassController::class, 'markAttendanceCheckout'])->name('checkout');

        Route::get("assigned-programs", [ProviderClassController::class, 'assigned_programs'])->name('assigned-programs');
        Route::get("assigned-program-details/{id}", [ProviderClassController::class, 'assigned_programs_details'])->name('assigned-program-details');


        Route::get("schedule", [ProviderClassController::class, 'schedule'])->name('schedule');
        Route::get("classes", [ProviderClassController::class, 'classess'])->name('classes');
        Route::get("student-attendance/{program_id}/{student_id}", [ProviderClassController::class, 'studentDetails'])->name('student.attendance');

        // Route::post("profile_update", [ParentController::class, 'profile_update'])->name('profile.update');



        Route::post('/paypal/capture', [ParentClassController::class, 'capturePayment'])->name('paypal.capture');
        Route::get("logout", [ProviderController::class, 'logout'])->name('logout');

        Route::get('/program-edit/{id}', [ProviderController::class, 'programEdit'])->name('program.edit');

        Route::post('/program-update/{id}', [ProviderController::class, 'programUpdate'])->name('program.update');

        // Service Provider Setup Routes
        Route::prefix('setup')->name('setup.')->group(function () {
            Route::get('/', [App\Http\Controllers\Provider\ProviderSetupController::class, 'index'])->name('index');
        });


        // Class management
        Route::post('/classes', [ProviderClassController::class, 'storeClass'])->name('store_class');
        Route::get('/classes/{id}', [ProviderClassController::class, 'class_details'])->name('class_details');
        Route::get('/classes/{id}/edit', [ProviderClassController::class, 'editClass'])->name('edit_class');
        Route::put('/classes/{id}', [ProviderClassController::class, 'updateClass'])->name('update_class');
        Route::post('/classes/{id}/cancel', [ProviderClassController::class, 'cancelClass'])->name('cancel_class');
        Route::get("create-class", [ProviderClassController::class, 'createClass'])->name('create_class');
        Route::delete('/classes/{id}', [ProviderClassController::class, 'deleteClass'])->name('delete_class');

        // Blueprint management
        Route::get('/get-blueprint', [ProviderClassController::class, 'getBlueprint'])->name('get_blueprint');
        Route::post('/blueprints', [ProviderClassController::class, 'storeBlueprint'])->name('store_blueprint');
        Route::delete('/blueprints/{id}', [ProviderClassController::class, 'deleteBlueprint'])->name('delete_blueprint');

        // Draft management
        Route::post('/drafts', [ProviderClassController::class, 'saveDraft'])->name('save_draft');
        Route::get('/drafts/{id}/edit', [ProviderClassController::class, 'editDraft'])->name('edit_draft');
        Route::put('/drafts/{id}', [ProviderClassController::class, 'updateDraft'])->name('update_draft');
        Route::post('/drafts/{id}/publish', [ProviderClassController::class, 'publishDraft'])->name('publish_draft');
        Route::delete('/drafts/{id}', [ProviderClassController::class, 'deleteDraft'])->name('delete_draft');

        // School request management
        Route::get('/requests/{id}', [ProviderClassController::class, 'requestDetails'])->name('request_details');
        Route::post('/requests/{id}/respond', [ProviderClassController::class, 'respondToRequest'])->name('respond_to_request');
        //  attendance 
        Route::get('/attendance', [ProviderClassController::class, 'attendance'])->name('attendance');
        Route::get('/attendance/{classId}', [ProviderClassController::class, 'getAttendance'])->name('get.attendance');
        Route::post('/attendance/{classId}', [ProviderClassController::class, 'updateAttendance'])->name('update.attendance');
        Route::post('/attendance/{classId}/bulk', [ProviderClassController::class, 'bulkAttendanceUpdate'])->name('bulk.attendance.update');

        // class catelogue
        Route::get('class-catalogue', [ProviderClassController::class, 'classCatalogue'])->name('class.catalogue');
        Route::get('new-class-catalogue', [ProviderClassController::class, 'newClassCatalogue'])->name('new.class.catalogue');
        Route::get('/class-catalogue/{id}/edit', [ProviderClassController::class, 'editClassCatalogue'])->name('edit_class_catalogue');
        Route::put('/class-catalogue/{id}', [ProviderClassController::class, 'updateClassCatalogue'])->name('update_class_catalogue');
        Route::delete('/class-catalogue/{id}', [ProviderClassController::class, 'deleteClassCatalogue'])->name('delete_class_catalogue');
        Route::post('/new-class-catalogue', [ProviderClassController::class, 'createClassCatalogue'])->name('create_class_catalogue');


        // my schools tab
        Route::get('/my-schools', [ProviderClassController::class, 'mySchools'])->name('my_schools');
        Route::get('/school/{id}/history', [ProviderClassController::class, 'schoolHistory'])->name('school_history');
        Route::get('/school/{id}/details', [ProviderClassController::class, 'mySchoolDetails'])->name('my_school_details');
        Route::get('/school/{id}/compliance', [ProviderClassController::class, 'schoolCompliance'])->name('school_compliance');

        //reports
        Route::get('/reports', [ProviderClassController::class, 'reports'])->name('reports');
        Route::get('/network', [ProviderClassController::class, 'network'])->name('network');

        // Provider Messages Routes
        Route::group(['prefix' => 'messages', 'as' => 'messages.'], function () {
            Route::get('/', [App\Http\Controllers\Provider\MessageController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Provider\MessageController::class, 'create'])->name('create');
            Route::post('/send', [App\Http\Controllers\Provider\MessageController::class, 'send'])->name('send');
            Route::get('/get-recipients', [App\Http\Controllers\Provider\MessageController::class, 'getRecipients'])->name('get-recipients');
        });

        Route::get('/students/search', [ProviderClassController::class, 'searchStudents'])->name('students.search');

        // send email to instructor 
        Route::post('/send-email', [App\Http\Controllers\Provider\MessageController::class, 'sendEmail'])->name('send-email');

        Route::get('/messages', [App\Http\Controllers\Provider\MessageController::class, 'index'])->name('messages.index');
        Route::get('/messages/create', [App\Http\Controllers\Provider\MessageController::class, 'create'])->name('messages.create');
        Route::post('/messages/send', [App\Http\Controllers\Provider\MessageController::class, 'send'])->name('messages.send');
        Route::get('/messages/broadcast', [App\Http\Controllers\Provider\MessageController::class, 'broadcastForm'])->name('messages.broadcast');
        Route::post('/messages/broadcast/send', [App\Http\Controllers\Provider\MessageController::class, 'broadcast'])->name('messages.broadcast.send');
        Route::post('/messages/get-recipients', [App\Http\Controllers\Provider\MessageController::class, 'getRecipients'])->name('messages.get-recipients');
        Route::get('/messages/chat', [App\Http\Controllers\Provider\MessageController::class, 'chat'])->name('messages.chat');
    });
    // bulk checkin
    Route::post("bulk_checkin", [ProviderClassController::class, 'markAttendance'])->name('bulk_checkin');
    Route::post("checkout", [ProviderClassController::class, 'markAttendanceCheckout'])->name('checkout');
});

// instructor-attendance-pages-link
Route::get("instructor/current-class-details/{id}", [ProviderInstructorController::class, 'current_class_details'])->name('instructor.current.class.details');
Route::get("instructor/program-attendance/{id}", [ProviderInstructorController::class, 'program_attendance'])->name('instructor.program.attendance');
Route::get("instructor/student-attendance/{program_id}/{student_id}", [ProviderInstructorController::class, 'studentDetails'])->name('instructor.student.attendance');

Route::post("bulk_checkin", [ProviderClassController::class, 'markAttendance'])->name('bulk_checkin');


// ******************************** School Admin Routes *************************************
Route::group(['prefix' => "school", 'as' => 'school.'], function () {
    Route::get("/", [SchoolController::class, 'signin'])->name('login');

    Route::get("signin", [SchoolController::class, 'signin'])->name('signin');
    Route::post("signin_post", [SchoolController::class, 'signin_post'])->name('login.post');

    Route::get("signup", [SchoolController::class, 'signup'])->name('signup');
    Route::post("signup_post", [SchoolController::class, 'signup_post'])->name('signup.post');

    Route::get("reset", [SchoolController::class, 'reset'])->name('reset');
    Route::post("reset_post", [SchoolController::class, 'reset_post'])->name('reset.post');

    Route::get("forget", [SchoolController::class, 'forget'])->name('forget');
    Route::post("forget_post", [SchoolController::class, 'forget_post'])->name('forget.post');


    Route::group(['middleware' => 'auth:school'], function () {
        Route::get("dashboard", [SchoolController::class, 'dashboard'])->name('dashboard');

        // Dashboard with session filter
        Route::get("dashboard", [SchoolController::class, 'dashboard'])->name('dashboard');

        // Student search route
        Route::get("students/search", [SchoolController::class, 'searchStudents'])->name('students.search');

        // Session details route
        Route::get("sessions/{id}/details", [SchoolController::class, 'getSessionDetails'])->name('sessions.details');

        Route::get("profile", [SchoolController::class, 'profile'])->name('profile');
        Route::post("profile_update", [SchoolController::class, 'profile_update'])->name('profile.update');

        Route::get("logout", [SchoolController::class, 'logout'])->name('logout');

        // CRUD RESOURCES ROUTES
        Route::resource("programs", SchoolProgramController::class);
        Route::resource("program-schedules", SchooolProgramScheduleController::class);
        Route::resource("enrollments", SchoolStudentController::class);
        Route::get("student-enrollments", [SchoolStudentController::class, 'enrollments'])->name('student.enrollments');

        Route::resource("service-providers", SchoolServiceProviderController::class);
        Route::resource("coupons", SchoolCouponController::class);
        Route::resource("teachers", SchoolTeacherController::class);


        // managing sessions
        Route::get("manage-sessions", [SchoolProgramController::class, 'manage_sessions'])->name('manage_sessions');
        Route::post("store-sessions", [SchoolProgramController::class, 'store_sessions'])->name('store_sessions');
        Route::post("update-sessions/{id}", [SchoolProgramController::class, 'update_sessions'])->name('update_sessions');
        Route::delete("delete-sessions/{id}", [SchoolProgramController::class, 'delete_sessions'])->name('delete_sessions');


        Route::get("current-class-details/{id}", [SchoolProgramController::class, 'current_class_details'])->name('current.class.details');
        Route::get("program-attendance/{id}", [SchoolProgramController::class, 'program_attendance'])->name('program.attendance');


        Route::post("bulk_checkin", [SchoolProgramController::class, 'markAttendance'])->name('bulk_checkin');
        Route::post("checkout", [SchoolProgramController::class, 'markAttendanceCheckout'])->name('checkout');
        // other routes
        Route::post('update-teacher', [SchoolStudentController::class, 'updateTeacher'])->name('update-teacher');
        Route::post('send-alert', [SchoolStudentController::class, 'sendLowAttendanceAlert'])->name('send-alert');
        Route::get('chart-data', [SchoolController::class, 'chartData'])->name('chart-data');

        // approve changes
        Route::post("program/approve-changes/{id}", [SchoolProgramController::class, 'approve_changes'])->name('program.approve_changes');

        // School Setup Routes
        Route::prefix('setup')->name('setup.')->group(function () {
            Route::get('/', [App\Http\Controllers\School\SetupController::class, 'index'])->name('index');
            Route::post('/school-info/{id}', [App\Http\Controllers\School\SetupController::class, 'updateSchoolInfo'])->name('school-info.update');

            // Team Member routes
            Route::post('/team-member/save', [App\Http\Controllers\School\SetupController::class, 'saveTeamMember'])->name('team-member.save');
            Route::delete('/team-member/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeamMember'])->name('team-member.delete');

            // Teacher routes
            Route::post('/teacher/save', [App\Http\Controllers\School\SetupController::class, 'saveTeacher'])->name('teacher.save');
            Route::delete('/teacher/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeacher'])->name('teacher.delete');

            // Policy routes
            Route::post('/policy/save', [App\Http\Controllers\School\SetupController::class, 'savePolicy'])->name('policy.save');
            Route::delete('/policy/delete', [App\Http\Controllers\School\SetupController::class, 'deletePolicy'])->name('policy.delete');

            // Waiver routes
            Route::post('/waiver/save', [App\Http\Controllers\School\SetupController::class, 'saveWaiver'])->name('waiver.save');
            Route::delete('/waiver/delete', [App\Http\Controllers\School\SetupController::class, 'deleteWaiver'])->name('waiver.delete');

            // Bank Account routes
            Route::post('bank-account/save', [App\Http\Controllers\School\SetupController::class, 'saveBankAccount'])->name('bank-account.save');
            Route::delete('bank-account/delete', [App\Http\Controllers\School\SetupController::class, 'deleteBankAccount'])->name('bank-account.delete');

            // School Fee routes
            Route::post('/school-fee/save', [App\Http\Controllers\School\SetupController::class, 'saveSchoolFee'])->name('school-fee.save');
            Route::delete('/school-fee/delete', [App\Http\Controllers\School\SetupController::class, 'deleteSchoolFee'])->name('school-fee.delete');

            // Tax Info routes
            Route::post('/tax-info/save', [App\Http\Controllers\School\SetupController::class, 'saveTaxInfo'])->name('tax-info.save');
            Route::delete('/tax-info/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTaxInfo'])->name('tax-info.delete');
        });

        // 
        // Provider Management Routes
        Route::resource('providers', SchoolServiceProviderController::class);
        Route::resource('instructors', AdminInstructorController::class);

        Route::post('providers/{provider}/documents', [SchoolServiceProviderController::class, 'storeDocument'])->name('providers.documents.store');
        Route::get('providers/documents/{document}/download', [SchoolServiceProviderController::class, 'downloadDocument'])->name('providers.documents.download');
        Route::post('providers/{provider}/instructors', [SchoolServiceProviderController::class, 'storeInstructor'])->name('providers.instructors.store');
        Route::get('providers/instructors/{id}', [SchoolServiceProviderController::class, 'showInstructor'])->name('providers.instructors.show');
        Route::post('providers/instructors/{instructor}/documents', [SchoolServiceProviderController::class, 'storeInstructorDocument'])->name('providers.instructors.documents.store');
        Route::post('providers/{provider}/requirements', [SchoolServiceProviderController::class, 'storeRequirement'])->name('providers.requirements.store');
        Route::post('providers/{provider}/scholarships', [SchoolServiceProviderController::class, 'storeScholarship'])->name('providers.scholarships.store');
        Route::put('providers/{provider}/scholarship', [SchoolServiceProviderController::class, 'updateScholarship'])->name('providers.scholarship.update');
        Route::post('providers/{provider}/reset-password', [SchoolServiceProviderController::class, 'resetPassword'])->name('providers.reset-password');


        //    reports routes
        Route::get('/reports', [App\Http\Controllers\School\ReportController::class, 'index'])->name('reports.index');
        Route::get('/reports/season-comparison', [App\Http\Controllers\School\ReportController::class, 'seasonComparison'])->name('reports.season-comparison');
        Route::get('/reports/season-data', [App\Http\Controllers\School\ReportController::class, 'getSeasonData'])->name('reports.season-data');
        Route::get('/reports/program/{id?}', [App\Http\Controllers\School\ReportController::class, 'programReport'])->name('reports.program');
        Route::get('/reports/program/{id}/export/{type}', [App\Http\Controllers\School\ReportController::class, 'exportProgramData'])->name('reports.program.export');

        Route::post('/send-mail', [App\Http\Controllers\School\MessageController::class, 'sendMail'])->name('send_mail');

        // Admin Messages Routes
        Route::group(['prefix' => 'messages', 'as' => 'messages.'], function () {
            Route::get('/', [App\Http\Controllers\School\MessageController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\School\MessageController::class, 'create'])->name('create');
            Route::post('/send', [App\Http\Controllers\School\MessageController::class, 'send'])->name('send');
            Route::get('/get-recipients', [App\Http\Controllers\School\MessageController::class, 'getRecipients'])->name('get-recipients');
        });


        // Admin Setup Routes
        Route::prefix('setup')->name('setup.')->group(function () {
            Route::get('/', [App\Http\Controllers\School\SetupController::class, 'index'])->name('index');
            Route::post('/school-info/{id}', [App\Http\Controllers\School\SetupController::class, 'updateSchoolInfo'])->name('school-info.update');

            // Team Member routes
            Route::post('/team-member/save', [App\Http\Controllers\School\SetupController::class, 'saveTeamMember'])->name('team-member.save');
            Route::delete('/team-member/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeamMember'])->name('team-member.delete');

            // Teacher routes
            Route::post('/teacher/save', [App\Http\Controllers\School\SetupController::class, 'saveTeacher'])->name('teacher.save');
            Route::delete('/teacher/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeacher'])->name('teacher.delete');

            // Policy routes
            Route::post('/policy/save', [App\Http\Controllers\School\SetupController::class, 'savePolicy'])->name('policy.save');
            Route::delete('/policy/delete', [App\Http\Controllers\School\SetupController::class, 'deletePolicy'])->name('policy.delete');

            // Waiver routes
            Route::post('/waiver/save', [App\Http\Controllers\School\SetupController::class, 'saveWaiver'])->name('waiver.save');
            Route::delete('/waiver/delete', [App\Http\Controllers\School\SetupController::class, 'deleteWaiver'])->name('waiver.delete');

            // Bank Account routes
            Route::post('bank-account/save', [App\Http\Controllers\School\SetupController::class, 'saveBankAccount'])->name('bank-account.save');
            Route::delete('bank-account/delete', [App\Http\Controllers\School\SetupController::class, 'deleteBankAccount'])->name('bank-account.delete');

            // School Fee routes
            Route::post('/school-fee/save', [App\Http\Controllers\School\SetupController::class, 'saveSchoolFee'])->name('school-fee.save');
            Route::delete('/school-fee/delete', [App\Http\Controllers\School\SetupController::class, 'deleteSchoolFee'])->name('school-fee.delete');

            // Tax Info routes
            Route::post('/tax-info/save', [App\Http\Controllers\School\SetupController::class, 'saveTaxInfo'])->name('tax-info.save');
            Route::delete('/tax-info/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTaxInfo'])->name('tax-info.delete');
        });

        // Attendance management routes
        Route::post('update-attendance', [SchoolController::class, 'updateAttendance'])->name('update_attendance');
        Route::post('send-attendance-sms', [SchoolController::class, 'sendAttendanceSms'])->name('send_attendance_sms');

        Route::get("student-attendance", [SchoolController::class, 'studentAttendance'])->name('student_attendance');
        Route::get("programs/{id}/get-attendance", [SchoolProgramController::class, 'getAttendance'])->name('programs.get_attendance');
    });
});

// ******************************** Super Admin Routes *************************************
Route::group(['prefix' => "admin", 'as' => 'admin.'], function () {
    Route::get("/", [AdminController::class, 'signin'])->name('login');

    Route::get("signin", [AdminController::class, 'signin'])->name('signin');
    Route::post("signin_post", [AdminController::class, 'signin_post'])->name('login.post');

    Route::get("signup", [AdminController::class, 'signup'])->name('signup');
    Route::post("signup_post", [AdminController::class, 'signup_post'])->name('signup.post');

    Route::get("reset", [AdminController::class, 'reset'])->name('reset');
    Route::post("reset_post", [AdminController::class, 'reset_post'])->name('reset.post');

    Route::get("forget", [AdminController::class, 'forget'])->name('forget');
    Route::post("forget_post", [AdminController::class, 'forget_post'])->name('forget.post');

    Route::get('/providers-instructors', [AdminProviderController::class, 'getProviderInstructors'])->name('providers.instructors.get');

    Route::group(['middleware' => 'auth:admin'], function () {
        Route::get("dashboard", [AdminController::class, 'dashboard'])->name('dashboard');

        Route::get("profile", [AdminController::class, 'profile'])->name('profile');
        Route::post("profile_update", [AdminController::class, 'profile_update'])->name('profile.update');

        Route::get("logout", [AdminController::class, 'logout'])->name('logout');
        Route::get("transactions", [AdminController::class, 'transactions'])->name('transactions');

        // CRUD RESOURCES ROUTES
        Route::resource("schools", AdminSchoolController::class);

        // managing sessions
        Route::get("school-requests", [AdminSchoolController::class, 'school_requests'])->name('school_requests');
        Route::get("school-request-details/{id}", [AdminSchoolController::class, 'school_request_show'])->name('school_request.details');
        Route::post("school-request-approve/{id}", [AdminSchoolController::class, 'school_request_approve'])->name('school_request.approve');

        Route::resource("kids", SchoolStudentController::class);
        Route::resource("programs", SchoolProgramController::class);
        Route::resource("service-providers", SchoolServiceProviderController::class);
        Route::resource("coupons", SchoolCouponController::class);
        Route::resource("teachers", SchoolTeacherController::class);
        Route::resource("program-schedules", SchooolProgramScheduleController::class);

        Route::get("programs/{id}/get-attendance", [SchoolProgramController::class, 'getAttendance'])->name('programs.get_attendance');

        Route::post("program-request-approve/{id}", [AdminSchoolController::class, 'program_request_approve'])->name('program_request.approve');

        // managing sessions
        Route::get("manage-sessions", [SchoolProgramController::class, 'manage_sessions'])->name('manage_sessions');
        Route::post("store-sessions", [SchoolProgramController::class, 'store_sessions'])->name('store_sessions');
        Route::post("update-sessions/{id}", [SchoolProgramController::class, 'update_sessions'])->name('update_sessions');
        Route::delete("delete-sessions/{id}", [SchoolProgramController::class, 'delete_sessions'])->name('delete_sessions');
        Route::get('/session/{id}/classes', [SchoolProgramController::class, 'sessionClasses'])->name('session.classes');

        Route::get("current-class-details/{id}", [SchoolProgramController::class, 'current_class_details'])->name('current.class.details');
        Route::get("program-attendance/{id}", [SchoolProgramController::class, 'program_attendance'])->name('program.attendance');


        Route::post("bulk_checkin", [SchoolProgramController::class, 'markAttendance'])->name('bulk_checkin');
        Route::post("checkout", [SchoolProgramController::class, 'markAttendanceCheckout'])->name('checkout');
        // other routes
        Route::post('update-teacher', [SchoolStudentController::class, 'updateTeacher'])->name('update-teacher');
        Route::post('send-alert', [SchoolStudentController::class, 'sendLowAttendanceAlert'])->name('send-alert');
        Route::get('chart-data', [AdminController::class, 'chartData'])->name('chart-data');

        // New enrollments
        Route::get("new_enrollment", [SchoolStudentController::class, 'new_enrollment'])->name('new_enrollment');
        Route::post("save_enrollment", [SchoolStudentController::class, 'save_enrollment'])->name('save_enrollment');

        Route::get("student-enrollments", [SchoolStudentController::class, 'enrollments'])->name('student.enrollments');


        // Admin To-Do Routes
        Route::get('todos', [AdminTodoController::class, 'index'])->name('todos.index');
        Route::post('todos', [AdminTodoController::class, 'store'])->name('todos.store');
        Route::patch('todos/{id}/status', [AdminTodoController::class, 'updateStatus'])->name('todos.updateStatus');
        Route::put('todos/{id}', [AdminTodoController::class, 'update'])->name('todos.update');
        Route::delete('todos/{id}', [AdminTodoController::class, 'destroy'])->name('todos.destroy');

        // earning routes
        Route::get('earnings', [AdminController::class, 'earnings'])->name('earnings');
        Route::get('enrollments', [AdminController::class, 'enrollments'])->name('enrollments');

        // school levels data update
        Route::put('school_level/{id}', [AdminController::class, 'school_level_update'])->name('school_level_update');

        // Provider Management Routes
        Route::resource('providers', AdminProviderController::class);
        Route::post('providers/{provider}/documents', [AdminProviderController::class, 'storeDocument'])->name('providers.documents.store');
        Route::get('providers/documents/{document}/download', [AdminProviderController::class, 'downloadDocument'])->name('providers.documents.download');
        Route::post('providers/{provider}/instructors', [AdminProviderController::class, 'storeInstructor'])->name('providers.instructors.store');
        Route::get('providers/instructors/{id}', [AdminProviderController::class, 'showInstructor'])->name('providers.instructors.show');
        Route::post('providers/instructors/{instructor}/documents', [AdminProviderController::class, 'storeInstructorDocument'])->name('providers.instructors.documents.store');
        Route::post('providers/{provider}/requirements', [AdminProviderController::class, 'storeRequirement'])->name('providers.requirements.store');
        Route::post('providers/{provider}/scholarships', [AdminProviderController::class, 'storeScholarship'])->name('providers.scholarships.store');
        Route::put('providers/{provider}/scholarship', [AdminProviderController::class, 'updateScholarship'])->name('providers.scholarship.update');
        Route::post('providers/{provider}/reset-password', [AdminProviderController::class, 'resetPassword'])->name('providers.reset-password');

        // Attendance management routes
        Route::post('update-attendance', [AdminController::class, 'updateAttendance'])->name('update_attendance');
        Route::post('send-attendance-sms', [AdminController::class, 'sendAttendanceSms'])->name('send_attendance_sms');

        //  instructors

        Route::get('/instructors', [AdminInstructorController::class, 'index'])->name('instructors.index');
        Route::get('/instructors/{id}', [AdminInstructorController::class, 'show'])->name('instructors.show');
        Route::post('/instructors/{id}/upload-document', [AdminInstructorController::class, 'uploadDocument'])->name('instructors.upload-document');
        Route::post('/instructors/{id}/upload-badge', [AdminInstructorController::class, 'uploadBadge'])->name('instructors.upload-badge');

        Route::get('/earnings/export', [AdminController::class, 'exportEarnings'])->name('earnings.export');
        Route::get('/schools/{id}/reports', [AdminSchoolController::class, 'reports'])->name('schools.reports');
        Route::get('/schools/{id}/reports/export', [AdminSchoolController::class, 'exportReports'])->name('schools.reports.export');

        Route::post('/admins', [AdminController::class, 'storeAdmin'])->name('admins.store');
        Route::put('/admins/{id}', [AdminController::class, 'updateAdmin'])->name('admins.update');
        Route::delete('/admins/{id}', [AdminController::class, 'destroyAdmin'])->name('admins.destroy');

        // Admin Setup Routes
        Route::prefix('setup')->name('setup.')->group(function () {
            Route::get('/', [App\Http\Controllers\School\SetupController::class, 'index'])->name('index');
            Route::post('/school-info/{id}', [App\Http\Controllers\School\SetupController::class, 'updateSchoolInfo'])->name('school-info.update');

            // Team Member routes
            Route::post('/team-member/save', [App\Http\Controllers\School\SetupController::class, 'saveTeamMember'])->name('team-member.save');
            Route::delete('/team-member/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeamMember'])->name('team-member.delete');

            // Teacher routes
            Route::post('/teacher/save', [App\Http\Controllers\School\SetupController::class, 'saveTeacher'])->name('teacher.save');
            Route::delete('/teacher/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTeacher'])->name('teacher.delete');

            // Policy routes
            Route::post('/policy/save', [App\Http\Controllers\School\SetupController::class, 'savePolicy'])->name('policy.save');
            Route::delete('/policy/delete', [App\Http\Controllers\School\SetupController::class, 'deletePolicy'])->name('policy.delete');

            // Waiver routes
            Route::post('/waiver/save', [App\Http\Controllers\School\SetupController::class, 'saveWaiver'])->name('waiver.save');
            Route::delete('/waiver/delete', [App\Http\Controllers\School\SetupController::class, 'deleteWaiver'])->name('waiver.delete');

            // Bank Account routes
            Route::post('bank-account/save', [App\Http\Controllers\School\SetupController::class, 'saveBankAccount'])->name('bank-account.save');
            Route::delete('bank-account/delete', [App\Http\Controllers\School\SetupController::class, 'deleteBankAccount'])->name('bank-account.delete');

            // School Fee routes
            Route::post('/school-fee/save', [App\Http\Controllers\School\SetupController::class, 'saveSchoolFee'])->name('school-fee.save');
            Route::delete('/school-fee/delete', [App\Http\Controllers\School\SetupController::class, 'deleteSchoolFee'])->name('school-fee.delete');

            // Tax Info routes
            Route::post('/tax-info/save', [App\Http\Controllers\School\SetupController::class, 'saveTaxInfo'])->name('tax-info.save');
            Route::delete('/tax-info/delete', [App\Http\Controllers\School\SetupController::class, 'deleteTaxInfo'])->name('tax-info.delete');
        });
    });
});


Route::get("get-teachers", [AjaxController::class, "getTeacherBySchool"])->name('get-teachers');
Route::get("get-students", [AjaxController::class, "getStudentByTeacher"])->name('get-students');
Route::get("get-states", [AjaxController::class, "getStates"])->name('get-states');


Route::group(['prefix' => "common", 'as' => 'common.', 'middleware' => ['auth:school']], function () {
    Route::post("bank_update/{id}", [CommonController::class, 'bank_update'])->name('bank.update');
    Route::post("bank_store", [CommonController::class, 'bank_store'])->name('bank.store');
    Route::delete("bank_delete/{id}", [ProviderController::class, 'bank_delete'])->name('bank.delete');
});

// School Enrollment Filter Routes
Route::group(['prefix' => 'school', 'as' => 'school.', 'middleware' => ['auth:school']], function () {
    // Enrollment filter routes
    Route::get('enrollments_/by-activity', [App\Http\Controllers\School\SchoolStudentController::class, 'byActivity'])
        ->name('enrollments.by.activity');
    Route::get('enrollments_/by-teacher', [App\Http\Controllers\School\SchoolStudentController::class, 'byTeacher'])
        ->name('enrollments.by.teacher');
    Route::get('enrollments_/by-dismissal', [App\Http\Controllers\School\SchoolStudentController::class, 'byDismissal'])
        ->name('enrollments.by.dismissal');

    // Email and message routes
    Route::post('get-emails', [App\Http\Controllers\School\SchoolStudentController::class, 'getEmails'])
        ->name('get.emails');
    Route::post('get-selected-data', [App\Http\Controllers\School\SchoolStudentController::class, 'getSelectedData'])
        ->name('get.selected.data');
    Route::post('send-message', [App\Http\Controllers\School\SchoolStudentController::class, 'sendMessage'])
        ->name('send.message');
});

// Route::get('/send-test-mail', function () {
//     $data = [
//         'bodyMessage' => 'This is a test email sent from Laravel.'
//     ];

//     Mail::send('emails.test', $data, function ($message) {
//         $message->to('<EMAIL>')
//             ->subject('Test Email');
//     });

//     return 'Test mail sent!';
// });


// cron for sending email to related program instructor before 15mins of the class start time
Route::get('/send-email-to-instructor', [AjaxController::class, 'sendEmailToInstructor'])->name('send.email.to.instructor');
