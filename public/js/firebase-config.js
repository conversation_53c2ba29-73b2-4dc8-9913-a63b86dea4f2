/**
 * Firebase Configuration and Utilities for One-to-One Chat System
 * This file provides common Firebase functionality for the Laravel chat system
 */

// Firebase configuration - these values should match your .env file
const firebaseConfig = {
    apiKey: "AIzaSyDRe1eViFofDKYqItv9OcWQUHZPKPdCMJk",
    authDomain: "chat-message-df24c.firebaseapp.com",
    projectId: "chat-message-df24c",
    storageBucket: "chat-message-df24c.firebasestorage.app",
    messagingSenderId: "976425238698",
    appId: "1:976425238698:web:433195679c737d445c90d3",
    measurementId: "G-Y6M5XP5CJS"
};

// Initialize Firebase (only if not already initialized)
if (!firebase.apps.length) {
    firebase.initializeApp(firebaseConfig);
}

// Get Firestore instance
const db = firebase.firestore();

/**
 * Firebase Chat Utilities
 */
class FirebaseChatUtils {
    
    /**
     * Generate consistent one-to-one chat ID
     * @param {string} senderType - Type of sender (parent, service_provider, school, admin)
     * @param {string} senderId - ID of sender
     * @param {string} recipientType - Type of recipient
     * @param {string} recipientId - ID of recipient
     * @returns {string} Chat ID
     */
    static generateOneToOneChatId(senderType, senderId, recipientType, recipientId) {
        const participants = [
            `${senderType}_${senderId}`,
            `${recipientType}_${recipientId}`
        ];
        participants.sort();
        return `one_to_one_${participants.join('_')}`;
    }
    
    /**
     * Send a one-to-one message
     * @param {string} chatId - Chat ID
     * @param {string} text - Message text
     * @param {string} senderId - Sender identifier
     * @param {string} senderName - Sender name
     * @param {string} senderType - Sender type
     * @param {string} recipientId - Recipient identifier
     * @param {string} recipientType - Recipient type
     * @returns {Promise}
     */
    static async sendOneToOneMessage(chatId, text, senderId, senderName, senderType, recipientId, recipientType) {
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();
        
        const messageData = {
            text: text,
            senderId: senderId,
            senderName: senderName,
            senderType: senderType,
            recipientId: recipientId,
            recipientType: recipientType,
            timestamp: timestamp,
            seen: false
        };
        
        try {
            // Add message to Firestore
            await db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .add(messageData);
            
            // Update chat metadata
            await db.collection("one_to_one_chats").doc(chatId).set({
                participants: [senderId, recipientId],
                lastMessage: text,
                lastMessageTime: Date.now(),
                lastMessageSender: senderId
            }, { merge: true });
            
            return { success: true };
        } catch (error) {
            console.error("Error sending message:", error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Listen to one-to-one messages
     * @param {string} chatId - Chat ID
     * @param {function} callback - Callback function to handle messages
     * @returns {function} Unsubscribe function
     */
    static listenToOneToOneMessages(chatId, callback) {
        return db.collection("one_to_one_chats")
            .doc(chatId)
            .collection("messages")
            .orderBy("timestamp")
            .onSnapshot((snapshot) => {
                const messages = [];
                snapshot.forEach((doc) => {
                    messages.push({ id: doc.id, ...doc.data() });
                });
                callback(messages);
            }, (error) => {
                console.error("Error listening to messages:", error);
                callback(null, error);
            });
    }
    
    /**
     * Mark messages as read
     * @param {string} chatId - Chat ID
     * @param {string} recipientId - Recipient identifier
     * @returns {Promise}
     */
    static async markMessagesAsRead(chatId, recipientId) {
        try {
            const querySnapshot = await db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .where("recipientId", "==", recipientId)
                .where("seen", "==", false)
                .get();
            
            const batch = db.batch();
            querySnapshot.forEach((doc) => {
                batch.update(doc.ref, { seen: true });
            });
            
            await batch.commit();
            return { success: true };
        } catch (error) {
            console.error("Error marking messages as read:", error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Get unread message count for a user
     * @param {string} userId - User identifier
     * @returns {Promise<number>}
     */
    static async getUnreadMessageCount(userId) {
        try {
            // This is a simplified version - in production, you might want to maintain
            // a separate collection for unread counts for better performance
            const chatsSnapshot = await db.collection("one_to_one_chats")
                .where("participants", "array-contains", userId)
                .get();
            
            let totalUnread = 0;
            
            for (const chatDoc of chatsSnapshot.docs) {
                const messagesSnapshot = await chatDoc.ref
                    .collection("messages")
                    .where("recipientId", "==", userId)
                    .where("seen", "==", false)
                    .get();
                
                totalUnread += messagesSnapshot.size;
            }
            
            return totalUnread;
        } catch (error) {
            console.error("Error getting unread count:", error);
            return 0;
        }
    }
    
    /**
     * Get unread count for specific chat
     * @param {string} chatId - Chat ID
     * @param {string} userId - User identifier
     * @returns {Promise<number>}
     */
    static async getChatUnreadCount(chatId, userId) {
        try {
            const messagesSnapshot = await db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .where("recipientId", "==", userId)
                .where("seen", "==", false)
                .get();
            
            return messagesSnapshot.size;
        } catch (error) {
            console.error("Error getting chat unread count:", error);
            return 0;
        }
    }
    
    /**
     * Format timestamp for display
     * @param {firebase.firestore.Timestamp} timestamp - Firestore timestamp
     * @returns {string} Formatted time
     */
    static formatTimestamp(timestamp) {
        if (!timestamp) return '';
        
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        const now = new Date();
        const diffInHours = (now - date) / (1000 * 60 * 60);
        
        if (diffInHours < 24) {
            // Show time if within 24 hours
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) {
            // Show day of week if within a week
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            // Show date if older than a week
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    }
    
    /**
     * Validate message data
     * @param {Object} messageData - Message data to validate
     * @returns {Object} Validation result
     */
    static validateMessageData(messageData) {
        const required = ['text', 'senderId', 'senderName', 'senderType', 'recipientId', 'recipientType'];
        const validTypes = ['parent', 'service_provider', 'school', 'admin'];
        
        // Check required fields
        for (const field of required) {
            if (!messageData[field]) {
                return { valid: false, error: `Missing required field: ${field}` };
            }
        }
        
        // Validate text length
        if (messageData.text.length === 0 || messageData.text.length > 1000) {
            return { valid: false, error: 'Message text must be 1-1000 characters' };
        }
        
        // Validate user types
        if (!validTypes.includes(messageData.senderType) || !validTypes.includes(messageData.recipientType)) {
            return { valid: false, error: 'Invalid user type' };
        }
        
        return { valid: true };
    }
}

// Export for use in other files
window.FirebaseChatUtils = FirebaseChatUtils;
window.firebaseDb = db;
