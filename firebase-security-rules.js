// Firebase Security Rules for One-to-One Chat System
// This file contains the Firestore security rules for the Laravel one-to-one chat system

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // One-to-one chat collections
    match /one_to_one_chats/{chatId} {
      // Allow read/write if user is a participant in the chat
      allow read, write: if request.auth != null && 
        (request.auth.uid in resource.data.participants || 
         request.auth.uid in request.resource.data.participants);
      
      // Messages subcollection
      match /messages/{messageId} {
        // Allow read if user is sender or recipient
        allow read: if request.auth != null && 
          (request.auth.uid == resource.data.senderId || 
           request.auth.uid == resource.data.recipientId);
        
        // Allow create if user is authenticated and is the sender
        allow create: if request.auth != null && 
          request.auth.uid == request.resource.data.senderId &&
          request.resource.data.keys().hasAll(['text', 'senderId', 'senderName', 'senderType', 'recipientId', 'recipientType', 'timestamp', 'seen']) &&
          request.resource.data.text is string &&
          request.resource.data.text.size() > 0 &&
          request.resource.data.text.size() <= 1000 &&
          request.resource.data.senderId is string &&
          request.resource.data.senderName is string &&
          request.resource.data.senderType in ['parent', 'service_provider', 'school', 'admin'] &&
          request.resource.data.recipientId is string &&
          request.resource.data.recipientType in ['parent', 'service_provider', 'school', 'admin'] &&
          request.resource.data.seen is bool;
        
        // Allow update only for marking messages as read
        allow update: if request.auth != null && 
          request.auth.uid == resource.data.recipientId &&
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['seen']) &&
          request.resource.data.seen == true;
      }
    }
    
    // Legacy group chat messages (existing functionality)
    match /messages/{messageId} {
      // Allow read for authenticated users
      allow read: if request.auth != null;
      
      // Allow create for authenticated users with proper data structure
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId &&
        request.resource.data.keys().hasAll(['text', 'senderId', 'senderName', 'senderType', 'timestamp', 'chatId']) &&
        request.resource.data.text is string &&
        request.resource.data.text.size() > 0 &&
        request.resource.data.text.size() <= 1000;
    }
    
    // User presence and status
    match /user_status/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chat metadata for unread counts
    match /chat_metadata/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}

/*
FIREBASE SETUP INSTRUCTIONS:

1. Go to Firebase Console (https://console.firebase.google.com/)
2. Select your project or create a new one
3. Navigate to Firestore Database
4. Go to Rules tab
5. Replace the existing rules with the rules above
6. Publish the rules

COLLECTION STRUCTURE:

one_to_one_chats/{chatId}
├── participants: [string] - Array of participant IDs (e.g., ["parent_123", "service_provider_456"])
├── lastMessage: string - Last message text
├── lastMessageTime: timestamp - Last message timestamp
├── lastMessageSender: string - ID of last message sender
└── messages/{messageId}
    ├── text: string - Message content
    ├── senderId: string - Sender identifier (e.g., "parent_123")
    ├── senderName: string - Sender display name
    ├── senderType: string - Sender type (parent, service_provider, school, admin)
    ├── recipientId: string - Recipient identifier (e.g., "service_provider_456")
    ├── recipientType: string - Recipient type
    ├── timestamp: timestamp - Message timestamp
    └── seen: boolean - Whether message has been read

CHAT ID GENERATION:
Chat IDs are generated by sorting participant identifiers and joining them:
- Example: "one_to_one_parent_123_service_provider_456"
- This ensures consistent chat IDs regardless of who initiates the conversation

USER IDENTIFIER FORMAT:
- Parent: "parent_{user_id}"
- Service Provider: "service_provider_{user_id}"
- School: "school_{user_id}"
- Admin: "admin_{user_id}"

AUTHENTICATION:
- Users must be authenticated to access any chat data
- Users can only read/write messages where they are sender or recipient
- Users can only update messages to mark them as read (seen: true)

VALIDATION:
- Message text must be 1-1000 characters
- All required fields must be present
- User types must be valid (parent, service_provider, school, admin)
- Only the sender can create messages
- Only the recipient can mark messages as read
*/
