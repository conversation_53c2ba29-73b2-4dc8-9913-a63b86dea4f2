@extends('layouts.parents.app')

@push('css')
    <style>
        .messages-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .chat-sidebar {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 600px;
            overflow-y: auto;
        }
        
        .chat-main {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .conversation-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .conversation-item:hover {
            background-color: #f8f9fa;
        }
        
        .conversation-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .conversation-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .conversation-preview {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .conversation-time {
            color: #999;
            font-size: 12px;
        }
        
        .unread-badge {
            background: #ff4444;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            float: right;
        }
        
        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
        }
        
        .message-bubble {
            max-width: 70%;
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message-sent {
            background: #2196f3;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message-received {
            background: #f1f1f1;
            color: #333;
        }
        
        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ddd;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
@endpush

@section('content')
    <div class="messages-container">
        <div class="row">
            <div class="col-12 mb-4">
                <h2>Messages</h2>
                <p class="text-muted">Chat with your providers and schools</p>
            </div>
        </div>
        
        <div class="row">
            <!-- Conversations Sidebar -->
            <div class="col-md-4">
                <div class="chat-sidebar">
                    <div class="chat-header">
                        <h5 class="mb-0">Conversations</h5>
                    </div>
                    <div id="conversationsList">
                        <!-- Conversations will be loaded here -->
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading conversations...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chat Area -->
            <div class="col-md-8">
                <div class="chat-main">
                    <div class="chat-header" id="chatHeader" style="display: none;">
                        <h5 class="mb-0" id="chatTitle">Select a conversation</h5>
                        <small class="text-muted" id="chatSubtitle"></small>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <div class="empty-state">
                            <i class="fas fa-comments"></i>
                            <h5>Select a conversation to start messaging</h5>
                            <p>Choose a provider or school from the sidebar to begin chatting</p>
                        </div>
                    </div>
                    
                    <div class="chat-input" id="chatInput" style="display: none;">
                        <form id="messageForm">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDRe1eViFofDKYqItv9OcWQUHZPKPdCMJk",
            authDomain: "chat-message-df24c.firebaseapp.com",
            projectId: "chat-message-df24c",
            storageBucket: "chat-message-df24c.firebasestorage.app",
            messagingSenderId: "976425238698",
            appId: "1:976425238698:web:433195679c737d445c90d3",
            measurementId: "G-Y6M5XP5CJS"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        // Current user info
        const currentUserType = 'parent';
        const currentUserId = '{{ Auth::id() }}';
        const currentUserIdentifier = `${currentUserType}_${currentUserId}`;
        const currentUserName = '{{ Auth::user()->name }}';

        let currentChatId = null;
        let currentRecipient = null;

        // DOM elements
        const conversationsList = document.getElementById('conversationsList');
        const chatHeader = document.getElementById('chatHeader');
        const chatTitle = document.getElementById('chatTitle');
        const chatSubtitle = document.getElementById('chatSubtitle');
        const chatMessages = document.getElementById('chatMessages');
        const chatInput = document.getElementById('chatInput');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');

        // Load conversations on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
            setupMessageForm();
        });

        // Load available conversations (providers and schools)
        function loadConversations() {
            const providers = @json($providers);
            const schools = @json($schools);
            
            let conversationsHtml = '';
            
            // Add providers
            providers.forEach(provider => {
                conversationsHtml += `
                    <div class="conversation-item" onclick="selectConversation('service_provider', ${provider.id}, '${provider.name}', 'Provider')">
                        <div class="conversation-name">${provider.name}</div>
                        <div class="conversation-preview">Provider</div>
                        <div class="conversation-time">
                            <span class="unread-badge" id="unread-service_provider-${provider.id}" style="display: none;">0</span>
                        </div>
                    </div>
                `;
            });
            
            // Add schools
            schools.forEach(school => {
                conversationsHtml += `
                    <div class="conversation-item" onclick="selectConversation('school', ${school.id}, '${school.name}', 'School')">
                        <div class="conversation-name">${school.name}</div>
                        <div class="conversation-preview">School</div>
                        <div class="conversation-time">
                            <span class="unread-badge" id="unread-school-${school.id}" style="display: none;">0</span>
                        </div>
                    </div>
                `;
            });
            
            if (conversationsHtml === '') {
                conversationsHtml = `
                    <div class="text-center p-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                        <p class="text-muted">No conversations available</p>
                        <small>Enroll in programs to start messaging with providers and schools</small>
                    </div>
                `;
            }
            
            conversationsList.innerHTML = conversationsHtml;
        }

        // Select a conversation
        function selectConversation(recipientType, recipientId, recipientName, recipientLabel) {
            // Remove active class from all conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to selected conversation
            event.target.closest('.conversation-item').classList.add('active');
            
            // Set current recipient
            currentRecipient = {
                type: recipientType,
                id: recipientId,
                name: recipientName,
                label: recipientLabel
            };
            
            // Generate chat ID
            currentChatId = generateOneToOneChatId(currentUserType, currentUserId, recipientType, recipientId);
            
            // Update chat header
            chatTitle.textContent = recipientName;
            chatSubtitle.textContent = recipientLabel;
            chatHeader.style.display = 'block';
            chatInput.style.display = 'block';
            
            // Load messages for this conversation
            loadMessages(currentChatId);
        }

        // Generate consistent chat ID for one-to-one conversations
        function generateOneToOneChatId(senderType, senderId, recipientType, recipientId) {
            const participants = [
                `${senderType}_${senderId}`,
                `${recipientType}_${recipientId}`
            ];
            participants.sort();
            return `one_to_one_${participants.join('_')}`;
        }

        // Load messages for a chat
        function loadMessages(chatId) {
            chatMessages.innerHTML = '<div class="text-center p-4"><div class="spinner-border text-primary"></div></div>';
            
            // Set up real-time listener for messages
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .orderBy("timestamp")
                .onSnapshot((snapshot) => {
                    const messages = [];
                    snapshot.forEach((doc) => {
                        messages.push({ id: doc.id, ...doc.data() });
                    });
                    
                    renderMessages(messages);
                    
                    // Mark messages as read
                    markMessagesAsRead(chatId);
                }, (error) => {
                    console.error("Error loading messages:", error);
                    chatMessages.innerHTML = `
                        <div class="text-center p-4">
                            <i class="fas fa-exclamation-triangle text-warning fa-2x mb-3"></i>
                            <p>Error loading messages</p>
                        </div>
                    `;
                });
        }

        // Render messages in the chat
        function renderMessages(messages) {
            if (messages.length === 0) {
                chatMessages.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-comment-dots"></i>
                        <h5>No messages yet</h5>
                        <p>Start the conversation by sending a message</p>
                    </div>
                `;
                return;
            }
            
            let messagesHtml = '';
            messages.forEach(message => {
                const isOwn = message.senderId === currentUserIdentifier;
                const messageClass = isOwn ? 'message-sent' : 'message-received';
                const timestamp = message.timestamp ? new Date(message.timestamp.toDate()).toLocaleTimeString() : '';
                
                messagesHtml += `
                    <div class="message-bubble ${messageClass}">
                        <div>${message.text}</div>
                        <div class="message-time">${timestamp}</div>
                    </div>
                `;
            });
            
            chatMessages.innerHTML = messagesHtml;
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Setup message form
        function setupMessageForm() {
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const messageText = messageInput.value.trim();
                if (!messageText || !currentChatId || !currentRecipient) return;
                
                sendMessage(currentChatId, messageText);
                messageInput.value = '';
            });
        }

        // Send a message
        function sendMessage(chatId, text) {
            const timestamp = firebase.firestore.FieldValue.serverTimestamp();
            
            const messageData = {
                text: text,
                senderId: currentUserIdentifier,
                senderName: currentUserName,
                senderType: currentUserType,
                recipientId: `${currentRecipient.type}_${currentRecipient.id}`,
                recipientType: currentRecipient.type,
                timestamp: timestamp,
                seen: false
            };
            
            // Add message to Firestore
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .add(messageData)
                .then(() => {
                    // Update chat metadata
                    return db.collection("one_to_one_chats").doc(chatId).set({
                        participants: [currentUserIdentifier, `${currentRecipient.type}_${currentRecipient.id}`],
                        lastMessage: text,
                        lastMessageTime: Date.now(),
                        lastMessageSender: currentUserIdentifier
                    }, { merge: true });
                })
                .catch((error) => {
                    console.error("Error sending message:", error);
                    alert("Failed to send message. Please try again.");
                });
        }

        // Mark messages as read
        function markMessagesAsRead(chatId) {
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .where("recipientId", "==", currentUserIdentifier)
                .where("seen", "==", false)
                .get()
                .then((querySnapshot) => {
                    const batch = db.batch();
                    querySnapshot.forEach((doc) => {
                        batch.update(doc.ref, { seen: true });
                    });
                    return batch.commit();
                })
                .catch((error) => {
                    console.error("Error marking messages as read:", error);
                });
        }
    </script>
@endpush
