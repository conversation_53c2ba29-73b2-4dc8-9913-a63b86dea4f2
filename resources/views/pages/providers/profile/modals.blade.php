 <script>
     function deletePublic(ele) {
         var url = ele.getAttribute("data-url");

         var _token = '{{ csrf_token() }}';

         var obj = {

             _token
         };

         $.ajax({
             url: url,
             type: 'DELETE',
             data: obj,
             success: function(data) {
                 // console.log(data);
                 if (data.success) {
                     Swal.fire("Success", data.message, 'success').then((result) => {
                         if (result.value) {
                             var url = $('#redirect_url').val();
                             if (url !== undefined || url != null) {
                                 window.location = url;
                             } else {
                                 location.reload(true);
                             }

                         }
                     });
                 } else {
                     Swal.fire("Error", data.message, 'error').then((result) => {
                         if (result.value) {
                             var url = $('#redirect_url').val();
                             if (url !== undefined || url != null) {
                                 window.location = url;
                             } else {
                                 location.reload(true);
                             }

                         }
                     });;

                     // location.reload(true);

                 }
             }
         });
     }
 </script>

 <!-- Add New Bank -->
 <div class="modal ee-modal fade" id="AddnewBank" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
     <div class="modal-dialog">
         <div class="modal-content">
             <div class="modal-body">
                 <div class="ee-form-info">
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                     <h2>Add New Bank</h2>
                     <form action="{{ route('service_provider.bank.store') }}" id="create_bank_form" method="post">
                         @csrf
                         <div class="row">
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Bank Name</label>
                                     <input type="text" name="name" class="form-control" required
                                         placeholder="Bank Name">
                                 </div>
                             </div>


                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Routing Number</label>
                                     <input type="text" name="routing_number" class="form-control" required
                                         placeholder=" Routing Number">
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Account Number</label>
                                     <input type="text" name="account_number" class="form-control" required
                                         placeholder="Account Number">
                                 </div>
                             </div>


                             <div class="col-md-12">
                                 <div class="form-group">
                                     <button class="cancel-btn" data-bs-dismiss="modal" aria-label="Close"
                                         type="button" data-bs-dismiss="modal">Cancel</button>
                                     <button class="save-btn">Confirm</button>
                                 </div>
                             </div>
                         </div>
                     </form>
                 </div>
             </div>
         </div>
     </div>
 </div>

 <!-- Edit  New Bank -->
 <div class="modal ee-modal fade" id="EditnewBank" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
     <div class="modal-dialog">
         <div class="modal-content">
             <div class="modal-body">
                 <div class="ee-form-info">
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                     <h2>Edit Bank</h2>
                     <form action="{{ route('service_provider.bank.store') }}" id="edit_bank_form" method="post">
                         @csrf
                         <div class="row">
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Bank Name</label>
                                     <input type="text" name="name" id="name" class="form-control" required
                                         placeholder="Bank Name">
                                 </div>
                             </div>



                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Routing Number</label>
                                     <input type="text" name="routing_number" id="routing_number" required
                                         class="form-control" placeholder=" Routing Number">
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Account Number</label>
                                     <input type="text" name="account_number" id="account_number" required
                                         class="form-control" placeholder="Account Number">
                                 </div>
                             </div>


                             <div class="col-md-12">
                                 <div class="form-group">
                                     <button class="cancel-btn" data-bs-dismiss="modal" aria-label="Close"
                                         type="button" data-bs-dismiss="modal">Cancel</button>
                                     <button class="save-btn">Confirm</button>
                                 </div>
                             </div>
                         </div>
                     </form>
                 </div>
             </div>
         </div>
     </div>
 </div>




 <!-- Edit General Policies -->
 <div class="modal ee-modal fade" id="editGeneralPolicies" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
     <div class="modal-dialog">
         <div class="modal-content">
             <div class="modal-body">
                 <div class="ee-form-info">
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                     <h2>Edit General Policy</h2>
                     <form action="{{ route('service_provider.profile.update') }}" method="post"
                         id="edit_general_policies_form" enctype="multipart/form-data">
                         @csrf
                         <div class="row">
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>General Policy</label>
                                     <textarea name="general_policies" class="form-control" rows="3" placeholder="General Policy">{{ $parent->general_policies }}</textarea>
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <button class="cancel-btn" data-bs-dismiss="modal" aria-label="Close"
                                         type="button" data-bs-dismiss="modal">Cancel</button>
                                     <button class="save-btn" type="submit">Confirm</button>
                                 </div>
                             </div>
                         </div>
                     </form>
                 </div>
             </div>
         </div>
     </div>
 </div>


 <!-- Edit General Policies -->
 <div class="modal ee-modal fade" id="editCancellationPolicy" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
     <div class="modal-dialog">
         <div class="modal-content">
             <div class="modal-body">
                 <div class="ee-form-info">
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                     <h2>Edit Cancellation Policy</h2>
                     <form action="{{ route('service_provider.profile.update') }}" method="post"
                         id="edit_cancellation_policy_form" enctype="multipart/form-data">
                         @csrf
                         <div class="row">
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Cancellation Policy</label>
                                     <textarea name="cancellation_policy" class="form-control" rows="3" placeholder="Cancellation Policy">{{ $parent->cancellation_policy }}</textarea>
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <button class="cancel-btn" data-bs-dismiss="modal" aria-label="Close"
                                         type="button" data-bs-dismiss="modal">Cancel</button>
                                     <button class="save-btn" type="submit">Confirm</button>
                                 </div>
                             </div>
                         </div>
                     </form>
                 </div>
             </div>
         </div>
     </div>
 </div>
 <!-- Edit Profile -->
 <div class="modal ee-modal fade" id="editprofile" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
     <div class="modal-dialog">
         <div class="modal-content">
             <div class="modal-body">
                 <div class="ee-form-info">
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                     <h2>Edit Profile Detail</h2>
                     <form action="{{ route('service_provider.profile.update') }}" method="post" id="profile_form"
                         enctype="multipart/form-data">
                         @csrf
                         <div class="row">
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Name</label>
                                     <input type="text" name="name" class="form-control" placeholder="Name"
                                         value="{{ $parent->name }}" required>
                                 </div>
                             </div>
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Company Name</label>
                                     <input type="text" name="company_name" class="form-control"
                                         placeholder="Name" value="{{ $parent->company_name }}" required>
                                 </div>
                             </div>
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Email</label>
                                     <input type="text" name="email" value="{{ $parent->email }}" required
                                         class="form-control" placeholder="Email Address" readonly>
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Mobile</label>
                                     <input type="text" data-inputmask="'mask': '(*************'" name="mobile"
                                         value="{{ $parent->mobile }}" class="form-control"
                                         placeholder="(*************">
                                 </div>
                             </div>
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Website</label>
                                     <input type="text" name="website" value="{{ $parent->website }}"
                                         class="form-control" placeholder="Website">
                                 </div>
                             </div>
                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>About Us</label>
                                     <textarea name="about" class="form-control" rows="3" placeholder="About Us">{{ $parent->about }}</textarea>
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <label>Upload Profile Photo</label>
                                     <input type="file" name="profile" class="form-control">
                                 </div>
                             </div>

                             <div class="col-md-12">
                                 <div class="form-group">
                                     <button class="cancel-btn" data-bs-dismiss="modal" aria-label="Close"
                                         type="button" onclick="$('#editprofile').modal('hide')">Cancel</button>
                                     <button class="save-btn" type="submit">Confirm</button>
                                 </div>
                             </div>
                         </div>
                     </form>
                 </div>
             </div>
         </div>
     </div>
 </div>
 <script>
     function changeTeacher(val) {
         $.get("{{ route('get-students') }}?teacher_id=" + val, function(data) {
             var html =
                 `  <select class="form-control  select2" name="student_id" >`;
             html += `<option value="0" > Select Student</option>`;

             data.map(item => {
                 html += `<option value="${item.id}" > ${item.name}</option>`;
             });
             html += `</select>`;
             $("#student_container").html(html);
         });
     }
     $(document).ready(function() {

         $('.select2').select2(); // Initialize Select2

         $(":input").inputmask();

         $.validator.addMethod("customPhone", function(value, element) {
             return this.optional(element) || /^\(\d{3}\) \d{3}-\d{4}$/.test(value);
         }, "Please enter a valid mobile number");
         $.validator.addMethod("integer", function(value, element) {
             return value != 0;
         }, "Please select a valid option");

         $('#profile_form').validate({
             rules: {
                 email: {
                     required: true,
                     maxlength: 191,
                     email: true
                 },
                 mobile: {
                     customPhone: true,

                 },

             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#profile_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })

         $('#create_bank_form').validate({
             rules: {
                 name: {
                     required: true,
                     maxlength: 191,

                 },


             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#create_bank_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
         $('#edit_bank_form').validate({
             rules: {
                 name: {
                     required: true,
                     maxlength: 191,

                 },

             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#edit_bank_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
         $('#create_ins_form').validate({
             rules: {
                 name: {
                     required: true,
                     maxlength: 191,

                 },


             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#create_ins_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
         $('#edit_ins_form').validate({
             rules: {
                 name: {
                     required: true,
                     maxlength: 191,

                 },

             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#edit_bank_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
         $('#edit_general_policies_form').validate({
             rules: {
                 general_policies: {
                     required: true,
                     maxlength: 5000,
                 },


             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#profile_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
         $('#edit_cancellation_policy_form').validate({
             rules: {
                 cancellation_policy: {
                     required: true,
                     maxlength: 5000,
                 },


             },
             errorElement: "span",
             errorPlacement: function(error, element) {
                 error.addClass("text-danger ml-4");
                 element.closest(".form-group").append(error);
             },
             highlight: function(element, errorClass, validClass) {
                 $('.please-wait').click();
                 $(element).addClass("text-danger ml-4");
             },
             unhighlight: function(element, errorClass, validClass) {
                 $(element).removeClass("text-danger ml-4");
             },
             submitHandler: function(form, event) {
                 event.preventDefault();
                 let formData = new FormData(form);

                 $.ajax({
                     type: 'post',
                     url: form.action,
                     data: formData,
                     dataType: 'json',
                     contentType: false,
                     processData: false,

                     success: function(response) {
                         if (response.success) {

                             Swal.fire({
                                 title: 'Success',
                                 text: response.message,
                                 icon: 'success',

                             }).then((result) => {

                                 if (response.redirect == true) {
                                     window.location = response.route;
                                 }
                                 var url = $('#redirect_url').val();
                                 if (url !== undefined || url != null) {
                                     window.location = url;
                                 } else {
                                     location.reload(true);
                                 }
                             })

                             return false;
                         }

                         if (response.success == false) {
                             Swal.fire(
                                 'Error',
                                 response.message,
                                 'error'
                             );

                             return false;
                         }
                     },
                     error: function(data) {
                         if (data.status == 422) {
                             var form = $("#profile_form");
                             let li_htm = '';
                             $.each(data.responseJSON.errors, function(k, v) {
                                 const $input = form.find(
                                     `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                 );
                                 if ($input.next('small').length) {
                                     $input.next('small').html(v);
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').next('small').html(v);
                                     }
                                 } else {
                                     $input.after(
                                         `<small class='text-danger'>${v}</small>`
                                     );
                                     if (k == 'services' || k == 'membership') {
                                         $('#myselect').after(
                                             `<small class='text-danger'>${v[0]}</small>`
                                         );
                                     }
                                 }
                                 li_htm += `<li>${v}</li>`;
                             });

                             return false;
                         } else {
                             Swal.fire(
                                 'Error',
                                 data.statusText,
                                 'error'
                             );
                         }
                         return false;

                     }
                 });
             }
         })
     });

     function renderData(ele) {
         $("#EditnewBank").modal("show");
         $("#edit_bank_form").attr("action", ele.getAttribute("data-url"));
         $("#name").val(ele.getAttribute("data-name"));
         $("#bank_number").val(ele.getAttribute("data-bank_number"));
         $("#routing_number").val(ele.getAttribute("data-routing_number"));
         $("#account_number").val(ele.getAttribute("data-account_number"));


     }

     function renderInsData(ele) {
         $("#EditIns").modal("show");
         $("#edit_ins_form").attr("action", ele.getAttribute("data-url"));
         $("#ins_name").val(ele.getAttribute("data-name"));
         $("#ins_email").val(ele.getAttribute("data-email"));
         $("#ins_mobile").val(ele.getAttribute("data-mobile"));


     }

     function deletePublic(ele) {
         var url = ele.getAttribute("data-url");

         var _token = '{{ csrf_token() }}';

         var obj = {

             _token
         };

         $.ajax({
             url: url,
             type: 'DELETE',
             data: obj,
             success: function(data) {
                 // console.log(data);
                 if (data.success) {
                     Swal.fire("Success", data.message, 'success').then((result) => {
                         if (result.value) {
                             var url = $('#redirect_url').val();
                             if (url !== undefined || url != null) {
                                 window.location = url;
                             } else {
                                 location.reload(true);
                             }

                         }
                     });
                 } else {
                     Swal.fire("Error", data.message, 'error').then((result) => {
                         if (result.value) {
                             var url = $('#redirect_url').val();
                             if (url !== undefined || url != null) {
                                 window.location = url;
                             } else {
                                 location.reload(true);
                             }

                         }
                     });;

                     // location.reload(true);

                 }
             }
         });
     }
 </script>
 <script>
     $(document).ready(function() {
         // Initialize input masks
         $(":input").inputmask();

         // Initialize form validation
         initializeFormValidation();

         // Handle tab persistence
         var activeTab = localStorage.getItem('activeProviderTab');
         if (activeTab) {
             $('#providerTabs a[href="' + activeTab + '"]').tab('show');
         }

         // Store the active tab
         $('#providerTabs a').on('shown.bs.tab', function(e) {
             localStorage.setItem('activeProviderTab', $(e.target).attr('href'));
         });
     });
 </script>
