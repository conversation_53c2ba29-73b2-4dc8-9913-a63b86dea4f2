@extends('layouts.providers.app')
@push('css')
       <link rel="stylesheet" type="text/css" href="{{ asset('providers/css/classes.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('parents/css/home.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/program.css') }}">
@endpush
@section('content')
    <div class="programs-section">
        <div class="container">

            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('service_provider.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('service_provider.class.catalogue') }}">Class
                            Catalogue</a></li>
                    <li class="breadcrumb-item active"> {{ isset($blueprint) ? 'Edit' : 'Create' }} Blueprint</li>
                </ol>
            </nav>
            <div class="ee-head">
                <h3> {{ isset($blueprint) ? 'Edit' : 'Create' }} Blueprint</h3>
                <div class="search-filter ">
                    <div class="row g-2">
                        <div class="col-md-12">
                            <div class="form-group">

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ee-content">
                <div class="row g-2">
                    <div class="col-md-12">
                        <div class="manage-create-card">
                            <div class="ee-form-info">
                                <form
                                    action="{{ isset($blueprint) ? route('service_provider.update_class_catalogue', $blueprint->id) : route('service_provider.create_class_catalogue') }}"
                                    method="POST" id="create_class">
                                    @csrf
                                    @if (isset($blueprint))
                                        @method('PUT')
                                    @endif
                                    <div class="row">

                                        <div class="row">
                                            {{-- <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="school_id">School</label>
                                                            <select class="form-control" id="school_id" name="school_id" required>
                                                                <option value="">Select School</option>
                                                                @foreach ($schools as $school)
                                                                    <option value="{{ $school->id }}">{{ $school->name }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                     --}}



                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="title">Class Name</label>
                                                    <input type="text" class="form-control" id="title" name="title"
                                                        @if (isset($blueprint)) value="{{ $blueprint->title }}" @endif
                                                        required>
                                                </div>
                                            </div>



                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Start Time</label>
                                                    <input type="time" name="start_time" class="form-control"
                                                        value="{{ isset($blueprint) ? date('H:i', strtotime($blueprint->start_time)) : '' }}">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>End Time</label>
                                                    <input type="time" name="end_time" class="form-control"
                                                        value="{{ isset($blueprint) ? date('H:i', strtotime($blueprint->end_time)) : '' }}">
                                                </div>
                                            </div>

                                            @php
                                                $schedule =
                                                    isset($blueprint) && isset($blueprint->day_of_week)
                                                        ? $blueprint->day_of_week
                                                        : null;
                                                $daysArray = $blueprint ? explode(',', $blueprint->day_of_week) : [];
                                                $allDays = [
                                                    'Sunday',
                                                    'Monday',
                                                    'Tuesday',
                                                    'Wednesday',
                                                    'Thursday',
                                                    'Friday',
                                                    'Saturday',
                                                ];
                                                $isDaily = count(array_intersect($daysArray, $allDays)) === 7;
                                            @endphp

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label>Selected Days</label>
                                                    <div class="day-form-list">
                                                        <div class="days-list-item">
                                                            <div class="daycheckbox">
                                                                <input type="checkbox" id="Daily" name="daily"
                                                                    @checked($isDaily)>
                                                                <label for="Daily">
                                                                    <span class="checkbox-text">Daily</span>
                                                                </label>
                                                            </div>
                                                        </div>

                                                        @foreach ($allDays as $day)
                                                            <div class="days-list-item">
                                                                <div class="daycheckbox">
                                                                    <input type="checkbox" id="{{ substr($day, 0, 3) }}_"
                                                                        value="{{ $day }}" name="days[]"
                                                                        @checked(in_array($day, $daysArray))>
                                                                    <label for="{{ substr($day, 0, 3) }}_">
                                                                        <span
                                                                            class="checkbox-text">{{ substr($day, 0, 3) }}</span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>








                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label>Class Description</label>
                                                    <textarea class="form-control" name="description" placeholder="Class Description"> @if (isset($blueprint))
{{ $blueprint->description }}
@else
@endif
                                                    </textarea>
                                                </div>
                                            </div>


                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="min_students">Min Students</label>
                                                    <input type="number" class="form-control" id="min_students"
                                                        @if (isset($blueprint)) value="{{ $blueprint->min_students }}" @endif
                                                        name="min_students" required>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="max_students">Max Students</label>
                                                    <input type="number" class="form-control" id="max_students"
                                                        @if (isset($blueprint)) value="{{ $blueprint->max_students }}" @endif
                                                        name="max_students" required>
                                                </div>
                                            </div>



                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="age_range">Age Range</label>
                                                    <input type="text" class="form-control" id="age_range"
                                                        @if (isset($blueprint)) value="{{ $blueprint->age_range }}" @endif
                                                        name="age_range" placeholder="e.g. 8-12 years" required>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="cost">Cost ($)</label>
                                                    <input type="number" step="0.01" class="form-control"
                                                        @if (isset($blueprint)) value="{{ $blueprint->cost }}" @endif
                                                        id="cost" name="cost" required>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="supply_cost">Supply Cost ($)</label>
                                                    <input type="number" step="0.01" class="form-control"
                                                        @if (isset($blueprint)) value="{{ $blueprint->supply_cost }}" @endif
                                                        id="supply_cost" name="supply_cost" value="0">
                                                </div>
                                            </div>




                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">Classroom Location</label>
                                                    <input type="text" class="form-control" id="location"
                                                        @if (isset($blueprint)) value="{{ $blueprint->location }}" @endif
                                                        name="location" required>
                                                </div>
                                            </div>




                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="registration_questions">Additional Registration
                                                    Questions (Optional)</label>
                                                <textarea class="form-control" id="registration_questions" name="registration_questions" rows="3"
                                                    @if (isset($blueprint)) {{ $blueprint->registration_questions }} @endif
                                                    placeholder="Enter one question per line"></textarea>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" @checked(isset($blueprint) && $blueprint->enable_checkin)
                                                    type="checkbox" id="enable_checkin" name="enable_checkin"
                                                    value="1">
                                                <label class="form-check-label" for="enable_checkin">
                                                    Enable Instructor Check In/Out
                                                </label>
                                                <small class="d-block text-muted">
                                                    This allows the instructor to take attendance for this class
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group mt-4 d-flex justify-content-end">
                                       

                                        <button type="submit"
                                            class="btn-pur">{{ isset($blueprint) ? 'Update' : 'Create' }} Blueprint</button>
                                    </div>
                                </form>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
@endsection

@push('js')
    <script>
        $(document).ready(function() {
            $.validator.addMethod("integer", function(value, ele) {
                return value != 0;
            }, "Please select a valid option");
            $('#create_class').validate({
                rules: {
                    title: {
                        required: true,
                        maxlength: 191,
                    },
                    description: {
                        required: true,
                        maxlength: 500,
                    },
                    age_range: {
                        required: true,
                        maxlength: 191,
                    },
                    cost: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    supply_cost: {
                        number: true,
                        min: 0
                    },
                    min_students: {
                        required: true,
                        number: true,
                        min: 1
                    },
                    max_students: {
                        required: true,
                        number: true,
                        min: 1,
                      
                    },
                   
                    start_time: {
                        required: true,
                    },
                    end_time: {
                        required: true,
                       
                    },
                    location: {
                        required: true,
                        maxlength: 191,
                    }
                },
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.addClass("text-danger");
                    element.closest(".form-group").append(error);
                },
                highlight: function(element, errorClass, validClass) {
                    $('.please-wait').click();
                    $(element).addClass("text-danger");
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass("text-danger");
                },
                submitHandler: function(form, event) {
                    event.preventDefault();
                    let formData = new FormData(form);

                    $.ajax({
                        type: 'post',
                        url: form.action,
                        data: formData,
                        dataType: 'json',
                        contentType: false,
                        processData: false,

                        success: function(response) {
                            if (response.success) {

                                Swal.fire({
                                    title: 'Success',
                                    text: response.message,
                                    icon: 'success',

                                }).then((result) => {

                                    if (response.redirect == true) {
                                        window.location = response.route;
                                    }
                                    var url = $('#redirect_url').val();
                                    if (url !== undefined || url != null) {
                                        window.location = url;
                                    } else {
                                        location.reload(true);
                                    }
                                })

                                return false;
                            }

                            if (response.success == false) {
                                Swal.fire(
                                    'Error',
                                    response.message,
                                    'error'
                                );

                                return false;
                            }
                        },
                        error: function(data) {
                            if (data.status == 422) {
                                var form = $("#create_class");
                                let li_htm = '';
                                $.each(data.responseJSON.errors, function(k, v) {
                                    const $input = form.find(
                                        `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                    );
                                    if ($input.next('small').length) {
                                        $input.next('small').html(v);
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').next('small').html(v);
                                        }
                                    } else {
                                        $input.after(
                                            `<small class='text-danger'>${v}</small>`
                                        );
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').after(
                                                `<small class='text-danger'>${v[0]}</small>`
                                            );
                                        }
                                    }
                                    li_htm += `<li>${v}</li>`;
                                });

                                return false;
                            } else {
                                Swal.fire(
                                    'Error',
                                    data.statusText,
                                    'error'
                                );
                            }
                            return false;

                        }
                    });
                }
            })

        });
        $(document).ready(function() {

            if ($("#start_date")) {
                $("#start_date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
            if ($("#end_date")) {
                $("#end_date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    // yearRange: 'c:c-100', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
            if ($("#enrollment_start")) {
                $("#enrollment_start").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
            if ($("#enrollment_end")) {
                $("#enrollment_end").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    // yearRange: 'c:c-100', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
        })
    </script>

    <script>
        $(document).ready(function() {


            // Handle blueprint selection

            $('#blueprint_id').change(function() {
                var blueprintId = $(this).val();
                if (blueprintId) {
                    // Show loading indicator
                    Swal.fire({
                        title: 'Loading blueprint...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Fetch blueprint data via AJAX
                    $.ajax({
                        url: "{{ route('service_provider.get_blueprint') }}",
                        type: "GET",
                        data: {
                            blueprint_id: blueprintId
                        },
                        success: function(response) {
                            if (response.success) {
                                // Fill form fields with blueprint data
                                var data = response.data;
                                $('#title').val(data.title);
                                $('#description').val(data.description);
                                $('#age_range').val(data.age_range);
                                $('#cost').val(data.cost);
                                $('#supply_cost').val(data.supply_cost);
                                $('#min_students').val(data.min_students);
                                $('#max_students').val(data.max_students);
                                $('#day_of_week').val(data.day_of_week);
                                $('#start_time').val(data.start_time);
                                $('#end_time').val(data.end_time);
                                $('#location').val(data.location);
                                $('#registration_questions').val(data.registration_questions);

                                if (data.enable_checkin) {
                                    $('#enable_checkin').prop('checked', true);
                                }

                                Swal.close();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: 'Failed to load blueprint data'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while loading the blueprint'
                            });
                        }
                    });
                }
            });

            // Save as draft functionality
            $('#saveDraft').click(function() {
                // Change form action to draft route
                var form = $('.create-class-form');
                var originalAction = form.attr('action');
                form.attr('action', "{{ route('service_provider.save_draft') }}");

                // Submit the form
                form.submit();

                // Reset form action
                form.attr('action', originalAction);
            });

            // Delete draft confirmation
            $('.delete-draft').click(function() {
                var draftId = $(this).data('id');
                $('#deleteDraftForm').attr('action', "{{ route('service_provider.delete_draft', '') }}/" +
                    draftId);
                $('#deleteDraftModal').modal('show');
            });

            // Form validation
            $('.create-class-form').submit(function(e) {
                var startDate = new Date($('#start_date').val());
                var endDate = new Date($('#end_date').val());
                var enrollmentStart = new Date($('#enrollment_start').val());
                var enrollmentEnd = new Date($('#enrollment_end').val());

                if (startDate > endDate) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Dates',
                        text: 'Start date cannot be after end date'
                    });
                    return false;
                }

                if (enrollmentEnd > startDate) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Enrollment Dates',
                        text: 'Enrollment should end before the class starts'
                    });
                    return false;
                }

                return true;
            });
        });
        document.getElementById("Daily").addEventListener("change", function() {
            const checked = this.checked;
            const checkboxes = document.querySelectorAll('input[name="days[]"]');
            checkboxes.forEach((cb) => (cb.checked = checked));
        });
    </script>
    @php
        $selectedDates = isset($blueprint) ? explode(',', $blueprint->no_class_dates) : [];
    @endphp
    <script>
        function formatDateToMMDDYYYY(date) {
            const mm = String(date.getMonth() + 1).padStart(2, '0');
            const dd = String(date.getDate()).padStart(2, '0');
            const yyyy = date.getFullYear();
            return `${mm}-${dd}-${yyyy}`;
        }

        function getDatesBetween(start, end) {
            const date = new Date(start);
            const dates = [];

            while (date <= end) {
                dates.push(formatDateToMMDDYYYY(new Date(date)));
                date.setDate(date.getDate() + 1);
            }

            return dates;
        }

        function populateNoClassDates() {
            const startVal = $('#start_date').val();
            const endVal = $('#end_date').val();

            if (!startVal || !endVal) return;

            const [sm, sd, sy] = startVal.split('-');
            const [em, ed, ey] = endVal.split('-');

            const startDate = new Date(`${sy}-${sm}-${sd}`);
            const endDate = new Date(`${ey}-${em}-${ed}`);

            if (startDate > endDate) return;

            const dateOptions = getDatesBetween(startDate, endDate);

            const $select = $('#no_class_dates');
            $select.empty();
            var selectedDates = @json($selectedDates ?? []); // from backend
            dateOptions.forEach(date => {


                $select.append(new Option(date, date, false, selectedDates.includes(date)));

            });

            $select.select2({
                placeholder: "Select no class dates",
                allowClear: true
            });
            //  $select.trigger('change');
        }

        $(document).ready(function() {
            // Initialize Select2
            const $select = $('#no_class_dates');
            var selectedDates = @json($selectedDates ?? []); // from backend

            $select.select2({
                placeholder: "Select no class dates",
                allowClear: true
            });
            $select.val(selectedDates).trigger('change');

            // Hook into the datepicker's onSelect
            $("#start_date").datepicker("option", "onSelect", function() {
                populateNoClassDates();
            });

            $("#end_date").datepicker("option", "onSelect", function() {
                populateNoClassDates();
            });

            // Optional: preload if dates already set (e.g. during edit)
            populateNoClassDates();
        });
    </script>
    @if (request()->has('blueprint_id'))
        <script>
            $(document).ready(function() {
                $('#blueprint_id').val({{ request('blueprint_id') }}).trigger('change');
            });
        </script>
    @endif
@endpush
