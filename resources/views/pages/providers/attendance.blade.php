@extends('layouts.providers.app')

@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/providers.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('providers/css/attendance.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/home.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
@endpush

@section('content')
    <div class="attendance-main-content">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('service_provider.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Attendance</li>

            </ol>
        </nav>
        <!-- Date Navigation -->
        <div class="date-navigation mb-4">
            <h3>Class Attendance</h3>
            <div class="attendance-date-nav">
                <div class="nav-arrows">
                    <a
                        href="{{ route('service_provider.attendance', ['date' => $previousDayDate->toDateString()]) }}">&lt;</a>
                </div>
                <div class="date-display">
                    <h3>{{ $viewDate->format('F d, Y') }}</h3>
                    <input type="text" id="datePicker" class="form-control d-none">
                    <button id="todayButton">Today</button>
                    <button id="openDatePicker">
                        <i class="fas fa-calendar-alt"></i> </button>
                </div>
                <div class="nav-arrows">
                    <a href="{{ route('service_provider.attendance', ['date' => $nextDayDate->toDateString()]) }}">&gt;</a>
                </div>
            </div>
        </div>

        <!-- Attendance Summary Cards -->
        <div class="attendance-summary-cards mb-4">
            <div class="attendance-card total">
                <h2>Total Enrolled Today</h2>
                <div class="value">{{ $totalEnrolled }}</div>
                <small>Across all classes</small>
            </div>
            <div class="attendance-card checked-in">
                <h2>Checked In</h2>
                <div class="value">{{ $checkedIn }}</div>
                <small>{{ $totalEnrolled ? number_format(($checkedIn / $totalEnrolled) * 100, 1) : 0 }}% of
                    enrolled</small>
            </div>
            <div class="attendance-card not-checked-in">
                <h2>Not Checked In</h2>
                <div class="value">{{ $totalEnrolled - $checkedIn }}</div>
                <small>{{ $totalEnrolled ? number_format((($totalEnrolled - $checkedIn) / $totalEnrolled) * 100, 1) : 0 }}%
                    of
                    enrolled</small>
            </div>
            <div class="attendance-card checked-out">
                <h2>Checked Out</h2>
                <div class="value">{{ $checkedOut }}</div>
                <small>{{ $checkedIn ? number_format(($checkedOut / $checkedIn) * 100, 1) : 0 }}% of checked in</small>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section bg-white p-3 rounded shadow-sm mb-4">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="school">School</label>
                        <select class="form-control select2" id="school" name="school">
                            <option value="">All Schools</option>
                            @foreach ($all_schools as $school)
                                <option value="{{ $school->id }}" @selected(request()->has('school') && request('school') == $school->id)>{{ $school->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="class">Class</label>
                        <select class="form-control select2" id="class" name="class">
                            <option value="">All Classes</option>
                            @foreach ($all_classes as $class)
                                <option value="{{ $class->id }}" @selected(request()->has('class') && request('class') == $class->id)>{{ $class->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn-pur w-100">Apply Filters</button>
                    </div>
                </div>
                <div class="col-md-1">
                    {{-- reset --}}
                    <label>&nbsp;</label>
                    @include('partials.reset')

                </div>
            </form>
        </div>

        <!-- Classes List -->
        <div class="classes-list">
            @forelse($classes as $class)
                <div class="class-card bg-white p-3 rounded shadow-sm mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="attendance-classes-card">
                            <h4 class="mb-1">{{ $class->title }}</h4>
                            <div class="class-stats d-flex gap-4 text-muted mb-2">
                                <span>
                                    <i class="fas fa-clock"></i> {{ $class->start_time }} - {{ $class->end_time }}
                                </span>
                                <span>
                                    <i class="fas fa-users"></i> {{ $class->enrolled_count }} Enrolled
                                </span>
                                <span>
                                    <i class="fas fa-check-circle"></i> {{ $class->checked_in_count }} Checked In
                                </span>
                                <span>
                                    <i class="fas fa-sign-out-alt"></i> {{ $class->checked_out_count }} Checked Out
                                </span>
                            </div>
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar bg-success"
                                    style="width: {{ $class->enrolled_count ? ($class->checked_in_count / $class->enrolled_count) * 100 : 0 }}%"
                                    title="Checked In">
                                </div>
                                <div class="progress-bar bg-info"
                                    style="width: {{ $class->enrolled_count ? ($class->checked_out_count / $class->enrolled_count) * 100 : 0 }}%"
                                    title="Checked Out">
                                </div>
                            </div>
                        </div>
                        <a href="#" class="btn-pur take-attendance" data-class-id="{{ $class->id }}">Take

                            Attendance</a>
                        {{-- <a href="{{ $class->lessons->first() ? route('service_provider.program.attendance', $class->lessons->first()->id) : '#' }}"
                            class="btn-pur" data-class-id="{{ $class->id }}">Take
                            Attendance</a> --}}
                    </div>
                </div>
            @empty
                <div class="alert alert-info">No classes scheduled for this date.</div>
            @endforelse
        </div>
    </div>

    <!-- Attendance Modal -->
    <div class="modal fade" id="attendanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-Attendance-info">
                        <div class="ee-head">
                            <h3>Class Attendance</h3>
                            <div class="ml-auto">
                                <button type="button" class="btn-outline-gr" id="checkInAll">Check In All</button>
                                <button type="button" class="btn-outline-re" id="checkOutAll">Check Out All</button>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                        </div>
                        <div class="ee-card-table">
                            <table class="table" id="attendanceTable">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Check In</th>
                                        <th>Checked By</th>
                                        <th>Check Out</th>
                                        <th>Checked By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div class="ee-card-action">
                            <button type="button" class="btn-re" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn-pur" id="saveAttendance">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var currentClass = null;

            // Initialize date picker
            // Initialize Flatpickr for the date picker
            flatpickr("#datePicker", {
                dateFormat: "Y-m-d",
                defaultDate: "{{ $viewDate->toDateString() }}",
                onChange: function(selectedDates, dateStr, instance) {
                    if (selectedDates.length > 0) {
                        window.location.href = "{{ route('service_provider.attendance') }}?date=" +
                            dateStr;
                    }
                }
            });

            // Initialize time pickers
            flatpickr(".time-picker", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true
            });

            // Open date picker on icon click
            document.getElementById('openDatePicker').addEventListener('click', function() {
                document.getElementById('datePicker')._flatpickr.open();
            });

            // "Today" button click
            document.getElementById('todayButton').addEventListener('click', function() {
                window.location.href =
                    "{{ route('service_provider.attendance') }}"; // Redirect to today's date
            });

            // Handle Take Attendance button
            $('.take-attendance').click(function(e) {
                e.preventDefault();
                const classId = $(this).data('class-id');
                loadAttendanceData(classId);
            });

            // Check In All functionality
            $('#checkInAll').click(function() {
                currentTime = currentClass.program_schedule.start_time

                ;

                const currentUser = '{{ auth()->user()->name }}';

                $('#attendanceTable tbody tr').each(function() {
                    if (!$(this).find('.check-in-time').val()) {
                        $(this).find('.check-in-time').val(currentTime);
                        $(this).find('.check-in-by').text(currentUser);
                    }
                });
            });

            // Check Out All functionality
            $('#checkOutAll').click(function() {
                currentTime = currentClass.program_schedule.end_time;
                const currentUser = '{{ auth()->user()->name }}';

                $('#attendanceTable tbody tr').each(function() {
                    if (!$(this).find('.check-out-time').val()) {
                        $(this).find('.check-out-time').val(currentTime);
                        $(this).find('.check-out-by').text(currentUser);
                    }
                });
            });

            function loadAttendanceData(classId) {
                $.ajax({
                    url: `{{ route('login') }}/service-provider/attendance/${classId}`,
                    method: 'GET',
                    success: function(response) {
                        populateAttendanceTable(response.students);
                        currentClass = response.program;
                        console.log(response.program.program_schedule);

                        $('#attendanceModal').modal('show');
                    }
                });
            }

            function populateAttendanceTable(students) {
                const tbody = $('#attendanceTable tbody');
                tbody.empty();

                students.forEach(student => {
                    tbody.append(`
                <tr data-student-id="${student.id}">
                    <td>${student.name}</td>
                    <td><input type="time" class="form-control check-in-time" value="${student.check_in || ''}"></td>
                    <td class="check-in-by">${student.checked_in_by || ''}</td>
                    <td><input type="time" class="form-control check-out-time" value="${student.check_out || ''}"></td>
                    <td class="check-out-by">${student.checked_out_by || ''}</td>
                    <td>
                        <button class="btn-outline-gr check-in-btn">Check In</button>
                        <button class="btn-outline-re check-out-btn">Check Out</button>
                    </td>
                </tr>
            `);
                });

                // Handle Check In button clicks
                $('.check-in-btn').click(function() {
                    const row = $(this).closest('tr');
                    currentTime = currentClass.program_schedule.start_time;
                    //  currentTime = currentClass.program_schedule.start_time;
                    const currentUser = '{{ auth()->user()->name }}';

                    row.find('.check-in-time').val(currentTime);
                    row.find('.check-in-by').text(currentUser);
                });

                // Handle Check Out button clicks
                $('.check-out-btn').click(function() {
                    const row = $(this).closest('tr');
                    currentTime = currentClass.program_schedule.end_time;
                    const currentUser = '{{ auth()->user()->name }}';

                    row.find('.check-out-time').val(currentTime);
                    row.find('.check-out-by').text(currentUser);
                });
            }
            $('#saveAttendance').click(function() {
                const attendanceData = [];
                const classId = $('.take-attendance').data('class-id');
                const date = '{{ $viewDate->toDateString() }}';

                // Collect data from all rows
                $('#attendanceTable tbody tr').each(function() {
                    const row = $(this);
                    attendanceData.push({
                        student_id: row.data('student-id'),
                        check_in: row.find('.check-in-time').val(),
                        check_out: row.find('.check-out-time').val(),
                        checked_in_by: row.find('.check-in-by').text(),
                        checked_out_by: row.find('.check-out-by').text()
                    });
                });

                // Send data to server
                $.ajax({
                    url: `{{ route('login') }}/service-provider/attendance/${classId}/bulk`,
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    },
                    data: {
                        class_id: classId,
                        date: date,
                        attendance: attendanceData
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire("Success", response.message, "success").then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                            // Show success message
                            // alert('Attendance saved successfully');
                            // // Close the modal
                            // $('#attendanceModal').modal('hide');
                            // // Reload the page to update the attendance statistics
                            // location.reload();
                        } else {
                            Swal.fire("Error", response.message, "error");
                            // alert('Error: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('Error saving attendance: ' + xhr.responseText);
                    }
                });
            });
        });
    </script>
@endpush
