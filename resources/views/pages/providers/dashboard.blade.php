@extends('layouts.providers.app')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('providers/css/calendar.css') }}">
    <script type="text/javascript" src="{{ asset('parents/js/syncscroll.js') }}"></script>
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <style>
        .fc-event,
        .fc-event:hover {
            background: rgb(111, 81, 189);
            padding: 2px;
        }

        .fc-event-card {
            color: white
        }
          /* Student Search Styles */
        .student-search-container {
            margin-bottom: 20px;
        }

        .student-search-input {
            position: relative;
        }

        .student-search-input .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        #student-search-results {
            position: absolute;
            width: 100%;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 10px 20px;
        }

        .student-result-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            
        }

        .student-result-item:hover {
            background-color: #f5f5f5;
        }

    </style>
@endpush
@section('content')
    <div class="course-calendar-section">
        <!-- Student Search Bar -->
        <div class="student-search-container">
            <div class="student-search-input">
                <input type="text" id="student-search" class="form-control" placeholder="Search students ,activities and school...">
                <span class="search-icon"><img src="{{ asset('schools/images/search1.svg') }}"></span>
                <div id="student-search-results"></div>
            </div>
        </div>

        <div class="calendar-card">
            <div class="calendar-card-head">
                <h2>Schedule</h2>
            </div>
        </div>
        <div class="calendar-card-body">
            <div id="calendar"></div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');

            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: '{{ request()->has('date') ? 'timeGridDay' : 'timeGridWeek' }}',
                initialDate: '{{ date('Y-m-d') }}', // Or pass the specific date from your controller
                slotMinTime: "07:00:00",
                slotMaxTime: "22:00:00",
                allDaySlot: false,
                nowIndicator: true,
                events: @json($lessons_map),
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },

                // **KEY CHANGE HERE: Using eventContent to render custom HTML**
                eventContent: function(arg) {
                    let event = arg.event;
                    let extendedProps = event.extendedProps;

                    // Get start and end time in a readable format
                    let startTime = new Date(event.start).toLocaleTimeString([], {
                        hour: 'numeric',
                        minute: '2-digit'
                    });
                    let endTime = new Date(event.end).toLocaleTimeString([], {
                        hour: 'numeric',
                        minute: '2-digit'
                    });

                    let html = `
                <div class="fc-event-card">
                    <div class="fc-event-time">${extendedProps.start_time} - ${extendedProps.end_time}</div>
                    <div class="fc-event-title">${extendedProps.program}</div>
                    <div class="fc-event-details">
                        <p>School: ${extendedProps.school}</p>
                        <p>Students: ${extendedProps.total_kid}</p>
                    </div>
                </div>
            `;
                    return {
                        html: html
                    };
                },

                eventClick: function(info) {
                    if (info.event.extendedProps.updateUrl) {
                        location.href = info.event.extendedProps.updateUrl;

                    }
                }
            });

            calendar.render();

            // Session selector change handler
            // document.getElementById('session-selector').addEventListener('change', function() {
            //     var sessionId = this.value;
            //     if (sessionId) {
            //         var currentUrl = new URL(window.location.href);
            //         currentUrl.searchParams.set('session_id', sessionId);
            //         window.location.href = currentUrl.toString();
            //     }
            // });

            // Student search functionality
            var searchTimeout;
            var searchInput = document.getElementById('student-search');
            var searchResultsContainer = document.getElementById('student-search-results');

            searchInput.addEventListener('input', function() {
                var query = this.value;
                clearTimeout(searchTimeout);

                if (query.length < 2) {
                    searchResultsContainer.style.display = 'none';
                    return;
                }

                searchInput.addEventListener('input', function() {
                    var query = this.value;
                    clearTimeout(searchTimeout);

                    if (query.length < 2) {
                        searchResultsContainer.style.display = 'none';
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        fetch('{{ route('service_provider.students.search') }}?query=' +
                                encodeURIComponent(query))
                            .then(response => response.json())
                            .then(data => {
                                searchResultsContainer.innerHTML = '';

                                let hasResults = false;

                                // Students
                                if (data.students.length > 0) {
                                    let heading = document.createElement('div');
                                    heading.textContent = 'Students';
                                    heading.className = 'search-section-heading';
                                    searchResultsContainer.appendChild(heading);

                                    data.students.forEach(function(student) {
                                        let item = document.createElement(
                                        'div');
                                        item.className = 'student-result-item';
                                        item.textContent = student.name + ' (' +
                                            student.id + ')';
                                        item.addEventListener('click',
                                        function() {
                                            window.location.href =
                                                '#' +
                                                student.id;
                                        });
                                        searchResultsContainer.appendChild(
                                        item);
                                    });

                                    hasResults = true;
                                }

                                // Schools
                                if (data.schools.length > 0) {
                                    let heading = document.createElement('div');
                                    heading.textContent = 'Schools';
                                    heading.className = 'search-section-heading';
                                    searchResultsContainer.appendChild(heading);

                                    data.schools.forEach(function(school) {
                                        let item = document.createElement(
                                        'div');
                                        item.className = 'student-result-item';
                                        item.textContent = school.name +
                                            ' (ID: ' + school.id + ')';
                                        item.addEventListener('click',
                                        function() {
                                            window.location.href =
                                                '{{route('service_provider.login')}}'+'/school/'+ school.id +'/details/' 
                                        });
                                        searchResultsContainer.appendChild(
                                        item);
                                    });

                                    hasResults = true;
                                }

                                // Programs
                                if (data.programs.length > 0) {
                                    let heading = document.createElement('div');
                                    heading.textContent = 'Programs';
                                    heading.className = 'search-section-heading';
                                    searchResultsContainer.appendChild(heading);

                                    data.programs.forEach(function(program) {
                                        let item = document.createElement(
                                        'div');
                                        item.className = 'student-result-item';
                                        item.textContent = program.title +
                                            ' (ID: ' + program.id + ')';
                                        item.addEventListener('click',
                                        function() {
                                            window.location.href =
                                                '{{route('service_provider.login')}}'+'/class-details/' + program.id
                                        });
                                        searchResultsContainer.appendChild(
                                        item);
                                    });

                                    hasResults = true;
                                }

                                if (!hasResults) {
                                    let item = document.createElement('div');
                                    item.className = 'student-result-item';
                                    item.textContent = 'No matching results found';
                                    searchResultsContainer.appendChild(item);
                                }

                                searchResultsContainer.style.display = 'block';
                            });
                    }, 300);
                });

            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.student-search-container')) {
                    searchResultsContainer.style.display = 'none';
                }
            });

        });
    </script>
@endsection
