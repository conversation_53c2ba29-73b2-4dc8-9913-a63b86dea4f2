@extends('layouts.providers.app')
@push('css')
<link rel="stylesheet" type="text/css" href="{{ asset('providers/css/assigned-programs.css') }}">
<style>
    .class-catalogue-header {
    background: #f8f9fa;
    padding: 0 0 10px;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex
;
    align-items: center;
    justify-content: space-between;
}

    .class-catalogue-heading h3{font-size: 20px; font-weight: 600; margin: 0; padding: 0; color: var(--black);}
    .class-catalogue-heading p{font-size:14px; font-weight: normal; margin: 0; padding: 0; color: var(--black);}
    
    .class-item-card {
        display: flex;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.2s, box-shadow 0.2s;
        margin-bottom: 1.5rem;
    }
    
    .class-item-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .class-item-image {
        width: 240px;
        height: 180px;
        flex-shrink: 0;
    }
    
    .class-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .class-item-content {
        flex: 1;
        padding: 1.5rem;
        position: relative;
    }
    
    .class-item-content h2 {
        color: #2c3e50;
        font-size: 1.25rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .class-item-content p {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1rem;
    }
    
    .programs-KID {
        color: #6f51bd;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .programs-action {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        border-left: 1px solid #e9ecef;
    }
    
    .btn-pur {
        background: #6f51bd;
        color: #fff;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: background 0.2s;
    }
    
    .btn-pur:hover {
        background: #5b42a0;
        color: #fff;
    }
    
   
    
    .empty-state {
        text-align: center;
        padding: 4rem 0;
    }
    
    .empty-state img {
        max-width: 300px;
        margin-bottom: 1.5rem;
    }
    
    .empty-state p {
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    @media (max-width: 768px) {
        .class-item-card {
            flex-direction: column;
        }
        
        .class-item-image {
            width: 100%;
            height: 200px;
        }
        
        .programs-action {
            border-left: none;
            border-top: 1px solid #e9ecef;
        }
    }
</style>
@endpush

@section('content')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('service_provider.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Class Catalogue</li>

    </ol>
</nav>
<div class="class-catalogue-header">
    <div class="class-catalogue-heading">
        <h3 class="mb-0">Class Catalogue</h3>
        <p class="text-muted mb-0">Browse and use class blueprints for your programs</p>
    </div>
    <div class="search-filter">
        <div class="row g-2">
            <div class="col-md-5">
                <div class="form-group mb-0">
                    @include('partials.search')
                </div>
            </div>
            <div class="col-md-2">
                @include('partials.reset')
            </div>
            <div class="col-md-5">
                <div class="form-group mb-0">
                    <a class="btn-pur wd100" href="{{ route('service_provider.new.class.catalogue') }}">Create New</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="class-catalogue-section">
    @forelse ($blueprints as $item)
        <div class="class-item-card">
            <div class="class-item-image">
                <img src="{{ $item->image ? asset('uploads/programs/' . $item->image) : asset('images/no.svg') }}" 
                     alt="{{ $item->title }}">
            </div>
            <div class="class-item-content">
                <h2>{{ $item->title }}</h2>
                <p>
                    {{ Str::limit($item->description, 200) }}
                    @if (strlen($item->description) > 200)
                        <span class="text-primary cursor-pointer" 
                              onclick="readMore('{{ $item->description }}')">
                            read more
                        </span>
                    @endif
                </p>
                <div class="programs-KID">
                    <i class="fas fa-sync-alt me-1"></i>
                    {{ $item->usage_count }} times used
                </div>
            </div>
            <div class="programs-action">
                <a class="btn-pur" href="{{ route('service_provider.create_class', ['blueprint_id' => $item->id]) }}">
                    <i class="fas fa-plus-circle me-2"></i>
                    Use Blueprint
                </a>
                  <a class="btn-pur" href="{{ route('service_provider.edit_class_catalogue',  $item->id) }}">
                    <i class="fas fa-plus-circle me-2"></i>
                   Edit/View Details
                </a>
            </div>
        </div>
    @empty
        <div class="empty-state">
            <img src="{{ asset('images/no-records.svg') }}" alt="No blueprints found">
            <p>No class blueprints available yet</p>
        </div>
        @endforelse
</div>

@include('partials.read_more')
@endsection
