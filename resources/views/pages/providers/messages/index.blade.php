@extends('layouts.providers.app')
@push('css')
    <link rel="stylesheet" href="{{ asset('providers/css/messages.css') }}">

    <style>
        .list-group-item-action.active {
            background-color: #6f51bd !important;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
@endpush

@section('content')
    <div class="messages-main-content">
        <div class="messages-section">
            <div class="ee-head">
                <h3>Messages</h3>
                <div>
                    {{-- <a href="{{ route('service_provider.messages.chat') }}" class="btn btn-info me-2">
                        <i class="fas fa-comments me-1"></i> Chat
                    </a> --}}
                    <a href="{{ route('service_provider.messages.create') }}" class="btn-pur me-2">
                        <i class="fas fa-envelope me-1"></i> New Message
                    </a>
                    <a href="{{ route('service_provider.messages.broadcast') }}" class="btn-gr">
                        <i class="fas fa-bullhorn me-1"></i> Broadcast
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3">
                    <div class="card ee-card mb-4">
                        <div class="card-header text-white">
                            <h2>Message Categories</h2>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="{{ route('service_provider.messages.index', ['filter' => 'one_to_one']) }}"
                                class="list-group-item list-group-item-action {{ $filter == 'one_to_one' ? 'active' : '' }}">
                                <i class="fas fa-comments me-2"></i> One-to-One Chat
                            </a>
                            <a href="{{ route('service_provider.messages.index', ['filter' => 'all']) }}"
                                class="list-group-item list-group-item-action {{ $filter == 'all' ? 'active' : '' }}">
                                <i class="fas fa-inbox me-2"></i> All Messages
                            </a>
                            <a href="{{ route('service_provider.messages.index', ['filter' => 'programs']) }}"
                                class="list-group-item list-group-item-action {{ $filter == 'programs' ? 'active' : '' }}">
                                <i class="fas fa-graduation-cap me-2"></i> By Program
                            </a>
                            <a href="{{ route('service_provider.messages.index', ['filter' => 'schools']) }}"
                                class="list-group-item list-group-item-action {{ $filter == 'schools' ? 'active' : '' }}">
                                <i class="fas fa-school me-2"></i> By School
                            </a>
                            <a href="{{ route('service_provider.messages.index', ['filter' => 'instructors']) }}"
                                class="list-group-item list-group-item-action {{ $filter == 'instructors' ? 'active' : '' }}">
                                <i class="fas fa-chalkboard-teacher me-2"></i> By Instructor
                            </a>
                        </div>
                    </div>

                    @if ($filter == 'programs')
                        <div class="card ee-card mb-4">
                            <div class="card-header text-white">
                                <h2 class="mb-0">Select Program</h2>
                            </div>
                            <div class="list-group list-group-flush">
                                @foreach ($programs as $program)
                                    <a href="{{ route('service_provider.messages.index', ['filter' => 'programs', 'category' => $program->id]) }}"
                                        class="list-group-item list-group-item-action {{ $category == $program->id ? 'active' : '' }}">
                                        {{ $program->title }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if ($filter == 'schools')
                        <div class="card ee-card mb-4">
                            <div class="card-header text-white">
                                <h2 class="mb-0">Select School</h2>
                            </div>
                            <div class="list-group list-group-flush">
                                @foreach ($schools as $school)
                                    <a href="{{ route('service_provider.messages.index', ['filter' => 'schools', 'category' => $school->id]) }}"
                                        class="list-group-item list-group-item-action {{ $category == $school->id ? 'active' : '' }}">
                                        {{ $school->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if ($filter == 'instructors')
                        <div class="card ee-card mb-4">
                            <div class="card-header text-white">
                                <h2 class="mb-0">Select Instructor</h2>
                            </div>
                            <div class="list-group list-group-flush">
                                @foreach ($instructors as $instructor)
                                    <a href="{{ route('service_provider.messages.index', ['filter' => 'instructors', 'category' => $instructor->id]) }}"
                                        class="list-group-item list-group-item-action {{ $category == $instructor->id ? 'active' : '' }}">
                                        {{ $instructor->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    @if ($filter == 'one_to_one')
                        <div class="card ee-card mb-4">
                            <div class="card-header text-white">
                                <h2 class="mb-0">Select School to Chat</h2>
                            </div>
                            <div class="list-group list-group-flush" id="oneToOneSchoolsList">
                                @foreach ($schools as $school)
                                    <a href="javascript:void(0)"
                                       onclick="selectOneToOneChat('school', {{ $school->id }}, '{{ $school->name }}')"
                                       class="list-group-item list-group-item-action school-chat-item"
                                       data-school-id="{{ $school->id }}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ $school->name }}</strong>
                                                <br><small class="text-muted">School</small>
                                            </div>
                                            <span class="badge bg-danger unread-count" id="unread-school-{{ $school->id }}" style="display: none;">0</span>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="card ee-card">
                        <div class="card-header message-head-info">
                            <h2 class="mb-0" id="chatTitle">
                                @if ($filter == 'one_to_one')
                                    One-to-One Chat
                                @elseif ($filter == 'all')
                                    All Messages
                                @elseif($filter == 'programs' && $category)
                                    Messages for {{ $programs->find($category)->title ?? 'Program' }}
                                @elseif($filter == 'schools' && $category)
                                    Messages for {{ $schools->find($category)->name ?? 'School' }}
                                @else
                                    Messages
                                @endif
                            </h2>
                            <div class="message-head-search">
                                <input type="text" class="form-control" placeholder="Search messages...">
                                <button class="message-icon-search" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="chat-container" id="chatContainer" style="height: 600px; overflow-y: auto;">
                                <div class="messages-list" id="messagesList">
                                    <!-- Messages will be loaded here via Firebase -->
                                    <div class="text-center p-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-3">Loading messages...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="message-input-container message-footer-action">
                                <form id="messageForm" class="message-footer-form">
                                    <div class="message-footer-group">
                                        <input type="text" id="messageInput" class="form-control"
                                            placeholder="Type a message...">
                                        <button class="message-footer-send" type="submit">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "{{ env('FIREBASE_API_KEY') }}",
            authDomain: "{{ env('FIREBASE_AUTH_DOMAIN') }}",
            projectId: "{{ env('FIREBASE_PROJECT_ID') }}",
            storageBucket: "{{ env('FIREBASE_STORAGE_BUCKET') }}",
            messagingSenderId: "{{ env('FIREBASE_MESSAGING_SENDER_ID') }}",
            appId: "{{ env('FIREBASE_APP_ID') }}"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        // DOM elements
        const messagesList = document.getElementById('messagesList');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const chatContainer = document.getElementById('chatContainer');

        // Current user info
        const currentUserType = 'service_provider';
        const currentUserId = '{{ Auth::id() }}';
        const currentUserIdentifier = `${currentUserType}_${currentUserId}`;
        const currentUserName = '{{ Auth::user()->name }}';

        // One-to-one chat variables
        let currentOneToOneChatId = null;
        let currentRecipient = null;
        const isOneToOneMode = '{{ $filter }}' === 'one_to_one';

        // Select one-to-one chat with a school
        function selectOneToOneChat(recipientType, recipientId, recipientName) {
            // Remove active class from all school items
            document.querySelectorAll('.school-chat-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to selected school
            document.querySelector(`[data-school-id="${recipientId}"]`).classList.add('active');

            // Set current recipient
            currentRecipient = {
                type: recipientType,
                id: recipientId,
                name: recipientName
            };

            // Generate one-to-one chat ID
            currentOneToOneChatId = generateOneToOneChatId(currentUserType, currentUserId, recipientType, recipientId);

            // Update chat title
            document.getElementById('chatTitle').textContent = `Chat with ${recipientName}`;

            // Load one-to-one messages
            loadOneToOneMessages(currentOneToOneChatId);
        }

        // Generate consistent chat ID for one-to-one conversations
        function generateOneToOneChatId(senderType, senderId, recipientType, recipientId) {
            const participants = [
                `${senderType}_${senderId}`,
                `${recipientType}_${recipientId}`
            ];
            participants.sort();
            return `one_to_one_${participants.join('_')}`;
        }

        // Load one-to-one messages
        function loadOneToOneMessages(chatId) {
            messagesList.innerHTML = '<div class="text-center p-4"><div class="spinner-border text-primary"></div></div>';

            // Set up real-time listener for one-to-one messages
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .orderBy("timestamp")
                .onSnapshot((snapshot) => {
                    messagesList.innerHTML = '';
                    snapshot.forEach((doc) => {
                        const message = { id: doc.id, ...doc.data() };
                        displayOneToOneMessage(message);
                    });
                    chatContainer.scrollTop = chatContainer.scrollHeight;

                    // Mark messages as read
                    markOneToOneMessagesAsRead(chatId);
                }, (error) => {
                    console.error("Error loading one-to-one messages:", error);
                    messagesList.innerHTML = `
                        <div class="alert alert-danger m-3">
                            Error loading messages. Please try again later.
                        </div>
                    `;
                });
        }

        // Display one-to-one message
        function displayOneToOneMessage(message) {
            const isCurrentUser = message.senderId === currentUserIdentifier;
            const messageClass = isCurrentUser ? 'message-outgoing' : 'message-incoming';
            const alignClass = isCurrentUser ? 'align-self-end' : 'align-self-start';
            const bgClass = isCurrentUser ? ' text-white' : '';

            const messageElement = document.createElement('div');
            messageElement.className = `message ${messageClass} ${alignClass} m-2 p-3 rounded ${bgClass}`;
            messageElement.style.maxWidth = '75%';

            let timestamp = '';
            if (message.timestamp) {
                const date = message.timestamp.toDate ? message.timestamp.toDate() : new Date(message.timestamp);
                timestamp = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }

            messageElement.innerHTML = `
                <div class="message-content">
                    ${!isCurrentUser ? `<strong>${message.senderName}</strong><br>` : ''}
                    ${message.text}
                </div>
                <div class="message-time small text-${isCurrentUser ? 'light' : 'muted'} mt-1">
                    ${timestamp}
                    ${message.seen ? '<i class="fas fa-check-double ms-1"></i>' : '<i class="fas fa-check ms-1"></i>'}
                </div>
            `;

            messagesList.appendChild(messageElement);
        }

        // Mark one-to-one messages as read
        function markOneToOneMessagesAsRead(chatId) {
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .where("recipientId", "==", currentUserIdentifier)
                .where("seen", "==", false)
                .get()
                .then((querySnapshot) => {
                    const batch = db.batch();
                    querySnapshot.forEach((doc) => {
                        batch.update(doc.ref, { seen: true });
                    });
                    return batch.commit();
                })
                .catch((error) => {
                    console.error("Error marking messages as read:", error);
                });
        }

        // Get chat ID based on current filter
        function getChatId() {
            const filter = '{{ $filter }}';
            const category = '{{ $category }}';

            if (filter === 'all') {
                return 'all_messages';
            } else if (filter === 'programs' && category) {
                return `program_${category}`;
            } else if (filter === 'schools' && category) {
                return `school_${category}`;
            } else if (filter === 'instructors' && category) {
                return `instructor_${category}`;
            } else {
                return 'all_messages';
            }
        }

        // Load messages for the current chat
        function loadMessages() {
            const chatId = getChatId();
            messagesList.innerHTML = '';

            let query = db.collection('messages')
                .where('chatId', '==', chatId)
                .orderBy('timestamp', 'asc')
                .limit(100);

            if (chatId.startsWith('private_')) {
                query = db.collection('messages')
                    .where('participants', 'array-contains', currentUserIdentifier)
                    .orderBy('timestamp', 'asc')
                    .limit(100);
            }

            query.onSnapshot((snapshot) => {
                snapshot.docChanges().forEach((change) => {
                    if (change.type === 'added') {
                        const message = change.doc.data();
                        displayMessage(message);
                    }
                });
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }, (error) => {
                console.error("Error getting messages: ", error);
                messagesList.innerHTML = `
                <div class="alert alert-danger m-3">
                    Error loading messages. Please try again later.
                </div>
            `;
            });
        }

        // Display a single message
        function displayMessage(message) {
            const isCurrentUser = message.senderId === currentUserIdentifier;
            const messageClass = isCurrentUser ? 'message-outgoing' : 'message-incoming';
            const alignClass = isCurrentUser ? 'align-self-end' : 'align-self-start';
            const bgClass = isCurrentUser ? ' text-white' : '';

            const messageElement = document.createElement('div');
            messageElement.className = `message ${messageClass} ${alignClass} m-2 p-3 rounded ${bgClass}`;
            messageElement.style.maxWidth = '75%';

            const date = new Date(message.timestamp * 1000);
            const formattedTime = date.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });
            const formattedDate = date.toLocaleDateString();

            messageElement.innerHTML = `
            <div class="message-content">
                ${!isCurrentUser ? `<strong>${message.senderName}</strong><br>` : ''}
                ${message.text}
            </div>
            <div class="message-time small text-${isCurrentUser ? 'light' : 'muted'} mt-1">
                ${formattedTime} · ${formattedDate}
            </div>
            ${message.messageType === 'announcement' ? '<span class="badge bg-warning text-dark ms-2">Announcement</span>' : ''}
        `;

            messagesList.appendChild(messageElement);
        }

        // Send a new message
        messageForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const text = messageInput.value.trim();
            if (!text) return;

            if (isOneToOneMode && currentOneToOneChatId && currentRecipient) {
                // Send one-to-one message
                sendOneToOneMessage(currentOneToOneChatId, text);
            } else {
                // Send regular group message
                const chatId = getChatId();
                const timestamp = Math.floor(Date.now() / 1000);

                // Create message object
                const message = {
                    text: text,
                    senderId: currentUserIdentifier,
                    senderName: currentUserName,
                    senderType: currentUserType,
                    timestamp: timestamp,
                    chatId: chatId,
                    messageType: 'private', // Default for chat messages
                };

                // For private chats, add participants array
                if (chatId.startsWith('private_')) {
                    const recipientId = chatId.replace('private_', '').replace(currentUserIdentifier, '').replace('_',
                        '');
                    message.participants = [currentUserIdentifier, recipientId];
                }

                // Add message to Firestore
                db.collection('messages').add(message)
                    .then(() => {
                        messageInput.value = '';
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    })
                    .catch((error) => {
                        console.error("Error sending message: ", error);
                        alert("Failed to send message. Please try again.");
                    });
            }
        });

        // Send one-to-one message
        function sendOneToOneMessage(chatId, text) {
            const timestamp = firebase.firestore.FieldValue.serverTimestamp();

            const messageData = {
                text: text,
                senderId: currentUserIdentifier,
                senderName: currentUserName,
                senderType: currentUserType,
                recipientId: `${currentRecipient.type}_${currentRecipient.id}`,
                recipientType: currentRecipient.type,
                timestamp: timestamp,
                seen: false
            };

            // Add message to Firestore
            db.collection("one_to_one_chats")
                .doc(chatId)
                .collection("messages")
                .add(messageData)
                .then(() => {
                    // Update chat metadata
                    return db.collection("one_to_one_chats").doc(chatId).set({
                        participants: [currentUserIdentifier, `${currentRecipient.type}_${currentRecipient.id}`],
                        lastMessage: text,
                        lastMessageTime: Date.now(),
                        lastMessageSender: currentUserIdentifier
                    }, { merge: true });
                })
                .then(() => {
                    messageInput.value = '';
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                })
                .catch((error) => {
                    console.error("Error sending one-to-one message:", error);
                    alert("Failed to send message. Please try again.");
                });
        }

        // Load messages when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (isOneToOneMode) {
                // Show instruction for one-to-one mode
                messagesList.innerHTML = `
                    <div class="text-center p-5">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5>Select a school to start chatting</h5>
                        <p class="text-muted">Choose a school from the sidebar to begin one-to-one messaging</p>
                    </div>
                `;
            } else {
                loadMessages();
            }
        });
    </script>
@endpush
