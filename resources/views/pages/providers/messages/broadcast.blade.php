@extends('layouts.providers.app')
@push('css')
<link rel="stylesheet" href="{{ asset('providers/css/messages.css') }}">
@endpush
@section('content')
<div class="card ee-card">
    <div class="card-header">
        <h2>Broadcast Message</h2>
    </div>
    <div class="card-body composeMessage-form">
        <form action="{{ route('service_provider.messages.broadcast.send') }}" method="POST">
            @csrf
            
            <div class="form-group mb-3">
                <label for="delivery_method">Delivery Method</label>
                <select class="form-control" id="delivery_method" name="delivery_method" required>
                
                    <option value="email">Email</option>
                
                    {{-- <option value="both">All Methods</option> --}}
                </select>
            </div>
            
            <div class="form-group mb-3" id="subjectGroup" style="display: none;">
                <label for="subject">Subject</label>
                <input type="text" class="form-control" id="subject" name="subject">
            </div>
            
            <div class="form-group mb-3">
                <label for="message">Message</label>
                <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
            </div>
            
            <div class="alert alert-info">
                <p><strong>Note:</strong> This message will be sent to all schools, instructors, and parents associated with your programs.</p>
            </div>
            
            <div class="form-group">
                <button type="submit" class="save-btn">Send Broadcast</button>
                <a href="{{ route('service_provider.messages.index') }}" class="cancel-btn">Cancel</a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deliveryMethod = document.getElementById('delivery_method');
        const subjectGroup = document.getElementById('subjectGroup');
        
        deliveryMethod.addEventListener('change', function() {
            if (this.value === 'email' || this.value === 'both') {
                subjectGroup.style.display = 'block';
            } else {
                subjectGroup.style.display = 'none';
            }
        });
    });
</script>
@endsection