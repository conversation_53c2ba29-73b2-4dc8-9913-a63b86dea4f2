@extends('layouts.schools.app')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/program.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('parents/css/home.css') }}">
    <script type="text/javascript" src="{{ asset('parents/js/syncscroll.js') }}"></script>
    <link rel="stylesheet" type="text/css" href="{{ asset('parents/css/calendar.css') }}">
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <style>
        .ee-tab ul li {
            width: 33%;
            flex: 0 0 33%;
        }
    </style>
    <style>
        @keyframes spin {
            0% {
                transform: translate(-50%, -50%) rotate(0deg);
            }

            100% {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }
    </style>
@endpush
@section('content')
    <div id="loaderOverlay"
        style="
display: none;
position: fixed;
top: 0; left: 0;
width: 100vw; height: 100vh;
background-color: rgba(0, 0, 0, 0.5);
z-index: 9998;">
    </div>

    <!-- Loader Spinner -->
    <div id="loader"
        style="
display: none;
position: fixed;
top: 50%; left: 50%;
transform: translate(-50%, -50%);
border: 8px solid #f3f3f3;
border-top: 8px solid #3498db;
border-radius: 50%;
width: 60px; height: 60px;
animation: spin 1s linear infinite;
z-index: 9999;">
    </div>
    <div class="body-main-content">
        <div class="programs-section">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('school.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('school.programs.index') }}">Classes</a></li>

                        <li class="breadcrumb-item active" aria-current="page">Class Details</li>
                    </ol>
                </nav>

                <div class="ee-head">
                    <h3><a class="btn-gr mx-2" href="{{ route('school.programs.index') }}"><img
                                src="{{ asset('images/back.svg') }}" height="25" alt=""> </a> Class Details</h3>
                    <div class="search-filter wd40">
                        <div class="row g-2 justify-content-end">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <a class="btn-pur wd100"
                                        href="{{ route('school.program.attendance', $program->id) }}">View Class
                                        Attendance</a>
                                </div>
                            </div>

                            {{-- <div class="col-md-6">
                                <div class="form-group">
                                    <a class="btn-gr wd100"
                                        href="{{ route('school.current.class.details', $program->id) }}">Mark Class
                                        Attendance</a>
                                </div>
                            </div> --}}
                        </div>
                    </div>
                </div>

                <div class="ee-content">
                    <section class="course-single-section">
                        <div class="row">
                            <div class="col-lg-5">
                                <div class="course-image">
                                    <a href="">
                                        <img src="{{ asset("uploads/programs/$program->image") }}" />
                                    </a>
                                </div>
                            </div>
                            <input type="hidden" name="" id="redirect_url"
                                value="{{ route('school.programs.index') }}">

                            <div class="col-lg-7">
                                <div class="single-course-item">
                                    <h1 class="course-single-name">{{ $program->title }}</h1>

                                    <div class="course-providerssession-list">

                                        <div class="course-session-text">
                                            {{ $program->session }}
                                        </div>
                                    </div>
                                    <div class="fees-single-price">
                                        <span class="enrolment-price">Enrolment Fee
                                            {{ priceFormat($program->fee) }} </span>
                                        <span class="school-price">School Fee:
                                            {{ priceFormat($program->school->school_fee) }} </span>
                                        <span class="school-price">Platform Fee:
                                            {{ priceFormat($program->school->admin_fee) }} </span>
                                        <span class="total-price">Total Fees:
                                            {{ priceFormat($program->fee + $program->school->school_fee + $program->school->admin_fee) }}
                                        </span>
                                    </div>
                                    <div class="course-date-list">
                                        <div class="course-date-text"><img
                                                src="{{ asset('schools/images/calendar.svg') }}">
                                            Start Date:
                                            {{ date('M d, Y', strtotime($program->start_date)) }}
                                        </div>
                                        <div class="course-date-text"><img
                                                src="{{ asset('schools/images/calendar.svg') }}">
                                            End Date:
                                            {{ date('M d, Y', strtotime($program->end_date)) }}
                                        </div>
                                    </div>


                                    <div class="course-single-short-desc">
                                        <p>{{ $program->description }}</p>
                                    </div>

                                    @if ($program->status == 'Inactive')
                                        <div class="course-single-addtocart my-2">
                                            {{-- <button type="submit" class="btn-addtocart">Edit Program</button> --}}
                                            <button type="button" onclick="ApproveChanges()" class="btn-pur">Approve
                                                Changes</button>
                                        </div>
                                    @endif

                                    <div class="course-single-addtocart">
                                        <a type="submit" class="btn-pur"
                                            href="{{ route('school.programs.edit', encrypt($program->id)) }}">Edit
                                            Class</a>
                                        <a type="submit" class="btn-re" data-bs-toggle="modal"
                                            onclick="$('#delete_url').attr('data-url','{{ route('school.programs.destroy', $program->id) }}')"
                                            data-bs-target="#deletepop">Delete Class</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>


                    <div class="course-calendar-section">
                        <div class="ee-tab-1">
                            <ul class="nav nav-tabs">
                                <li><a @if (count(request()->all()) === 0) class="active" @endif href="#Schedule"
                                        data-bs-toggle="tab">Schedule</a></li>
                                {{-- <li><a href="#PaymentLog" data-bs-toggle="tab"
                                        @if (count(request()->all()) != 0) class="active" @endif>Payment Log</a></li> --}}
                                <li><a href="#Chat" data-bs-toggle="tab">Chat</a></li>
                                <li><a href="#ActivityInfo" data-bs-toggle="tab">Activity Info</a></li>
                                <li><a href="#Enrollment" data-bs-toggle="tab">Enrollment</a></li>
                                <li><a href="#Billing" data-bs-toggle="tab">Billing/Transactions</a></li>
                                <li><a href="#Attendance" data-bs-toggle="tab">Attendance</a></li>
                            </ul>
                        </div>


                        <div class="messages-tabs-content-info tab-content">
                            <div class="tab-pane @if (count(request()->all()) === 0) active @endif" id="Schedule">
                                <div class="calendar-card">
                                    <div class="ee-head">
                                        <h3>Schedule</h3>
                                        <div class="search-filter wd20">
                                            <div class="row g-2">
                                                <div class="col-md-12">
                                                    @if ($program->program_schedule)
                                                        <div class="form-group">
                                                            <a class="btn-outline-pur wd100" data-bs-toggle="modal"
                                                                data-bs-target="#addaclass">Add Lesson</a>
                                                        </div>
                                                    @endif

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="calendar-card-body" style="display: none">
                                        <svg style="position: absolute; width: 0; height: 0; display: none;">
                                            <symbol id="icon-arrow" viewBox="0 0 96 96">
                                                <title>Arrow</title>
                                                <path
                                                    d="M39.66,13.34A8,8,0,0,0,28.34,24.66L51.69,48,28.34,71.34A8,8,0,0,0,39.66,82.66l29-29a8,8,0,0,0,0-11.31Z" />
                                            </symbol>
                                        </svg>
                                        <div class="wrapper">
                                            <div class="table">
                                                <div class="headers">
                                                    <div class="buttons">
                                                        <button class="btn-left">
                                                            <svg>
                                                                <use xlink:href="#icon-arrow"></use>
                                                            </svg>
                                                        </button>
                                                        <button class="btn-right">
                                                            <svg>
                                                                <use xlink:href="#icon-arrow"></use>
                                                            </svg>
                                                        </button>
                                                    </div>

                                                    <div class="scroller syncscroll" name="myElements">
                                                        <div class="track time">
                                                            <div class="heading">Time</div>
                                                        </div>
                                                        @foreach ($days as $day)
                                                            <div class="track"
                                                                @if (date('d M', time()) === $day['date']) id="autofocus" @endif>
                                                                <div class="calendar-datetime-text">
                                                                    <h2>{{ $day['day'] }}</h2>
                                                                    <h4>{{ $day['date'] }}</h4>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>

                                                <div class="tracks syncscroll" name="myElements">
                                                    <div class="track time">
                                                        @foreach ($timeSlots as $time)
                                                            <div class="entry">
                                                                <time>{{ $time }}</time>
                                                            </div>
                                                        @endforeach
                                                    </div>

                                                    @foreach ($days as $day)
                                                        <div class="track"
                                                            @if (date('d M', time()) === $day['date']) id="autofocus" @endif>
                                                            @foreach ($timeSlots as $time)
                                                                <div class="entry">
                                                                    @php
                                                                        $lesson = $lessons->firstWhere(
                                                                            fn($l) => date(
                                                                                'l',
                                                                                strtotime($l->lesson_date),
                                                                            ) === $day['day'] &&
                                                                                date(
                                                                                    'h:i A',
                                                                                    strtotime($l->start_time),
                                                                                ) === $time,
                                                                        );

                                                                    @endphp

                                                                    @if ($lesson)
                                                                        <div class="game-card">
                                                                            <h3>{{ $lesson->title }}</h3>

                                                                            <div class="game-time">
                                                                                {{ date('h:i A', strtotime($lesson->start_time)) }}
                                                                                to
                                                                                {{ date('h:i A', strtotime($lesson->end_time)) }}
                                                                            </div>
                                                                            {{-- <small>{{ $lesson->class_name }}</small> --}}
                                                                            <buton class="btn btn-sm btn-primary"
                                                                                onclick="editLesson(this)"
                                                                                data-title="{{ $lesson->title }}"
                                                                                data-description="{{ $lesson->description }}"
                                                                                data-url="{{ route('school.program-schedules.update', $lesson->id) }}"
                                                                                data-lesson_date="{{ date('Y-m-d', strtotime($lesson->lesson_date)) }}"
                                                                                data-lesson_date="{{ $lesson->lesson_date }}">

                                                                                Edit
                                                                            </buton>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>

                                        </div>
                                        <div id="top-of-site-pixel-anchor"></div>
                                    </div>
                                    <div class="card-body">
                                        <div id="calendar"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane @if (count(request()->all()) != 0) active @endif" id="PaymentLog">
                                <div class="ee-head">
                                    <h3>Payment Log</h3>
                                    <div class="search-filter wd60">
                                        <div class="row g-2">
                                            <div class="col-md-3">
                                                @include('partials.export')

                                            </div>

                                            <div class="col-md-4">
                                                @include('partials.date_filter')
                                            </div>

                                            <div class="col-md-4">
                                                @include('partials.search')

                                            </div>
                                            <div class="col-md-1">
                                                @include('partials.reset')

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ee-content">
                                    <div class="ee-card">
                                        <div class="card-body">
                                            <div class="ee-card-table">
                                                <table class="table">
                                                    <thead>
                                                        <tr>
                                                            <th>S.no</th>
                                                            <th>Transaction ID</th>
                                                            <th>Amount Received</th>
                                                            <th>Kid Name & ID</th>
                                                            <th>Grade</th>
                                                            <th>Date & Time</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @forelse ($trs as $i=> $item)
                                                            <tr>
                                                                <td>{{ $i + 1 }}</td>
                                                                <td>{{ $item->transaction_id }}</td>
                                                                <td>{{ $item ? priceFormat($item->total) : 'N/A' }}
                                                                </td>
                                                                <td>{{ $item->student->name ?? 'N/A' }} </td>
                                                                <td>{{ $item->student->grade ?? 'N/A' }}</td>
                                                                <td> {{ date('d/m/Y h:i:s', strtotime($item->created_at)) }}
                                                                </td>
                                                            </tr>
                                                        @empty
                                                            <tr>
                                                                <td colspan="5" align="center">No records found</td>

                                                            </tr>
                                                        @endforelse


                                                    </tbody>
                                                </table>

                                                <div class="ee-table-pagination">
                                                    <ul class="ee-pagination">
                                                        <!-- Previous Button -->
                                                        <li class="{{ $trs->onFirstPage() ? 'disabled' : '' }}">
                                                            <a href="{{ $trs->previousPageUrl() ?: '#' }}"
                                                                class="page-link">Previous</a>
                                                        </li>

                                                        <!-- Page Numbers -->
                                                        @foreach ($trs->getUrlRange(1, $trs->lastPage()) as $page => $url)
                                                            <li
                                                                class="{{ $trs->currentPage() == $page ? 'active' : '' }}">
                                                                <a href="{{ $url }}"
                                                                    class="page-link">{{ $page }}</a>
                                                            </li>
                                                        @endforeach

                                                        <!-- Next Button -->
                                                        <li class="{{ $trs->hasMorePages() ? '' : 'disabled' }}">
                                                            <a href="{{ $trs->nextPageUrl() ?: '#' }}"
                                                                class="page-link">Next</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="Chat">
                                <div class="chat-panel-section">
                                    <div class="chat-panel-chat-header">
                                        <div class="chat-panel-user-item">
                                            <div class="chat-panel-user-item-image"><img
                                                    src="{{ asset('parents/images/profile.jpg') }}">
                                            </div>
                                            <div class="chat-panel-user-item-text">
                                                <h4>{{ $program->title }}</h4>
                                                {{-- <p>Emp Id: 210</p> --}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-panel-chat-body" tabindex="1"
                                        style="overflow: auto; outline: none;">
                                        <div class="chat-panel-chat-content">
                                            <div class="messages-list">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="chat-panel-chat-footer">
                                        <form>
                                            <div class="row">
                                                <div class="col-md-10">
                                                    <div class="form-group">
                                                        <input type="text" class="form-control input_box message_text"
                                                            placeholder="Write a message.">
                                                        <span class="form-attachemnt-icon">
                                                            <img src="{{ asset('parents/images/attach-circle.svg') }}">
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn-send" title="" type="button">
                                                        Send
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <!-- Activity Info Tab -->
                            <div class="tab-pane" id="ActivityInfo">
                                <div class="activity-section">
                                    <div class="ee-head">
                                        <h3>Activity Information</h3>
                                    </div>
                                    <div class="activity-content">
                                        <div class="row">
                                            <div class="col-md-6 mb-4">
                                                <div class="card ee-card">
                                                    <div class="card-header">
                                                        <h2> Basic Information</h2>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Class Name:</strong> {{ $program->title }}</p>
                                                        <p><strong>Provider:</strong> {{ $program->provider->name }}</p>
                                                        <p><strong>Provider Contact:</strong>
                                                            {{ $program->provider->email }}</p>
                                                        <p><strong>Provider Phone:</strong> {{ $program->provider->phone }}
                                                        </p>
                                                        <p><strong>Instructor:</strong>
                                                            {{ $programInstructor->instructor->name ?? 'Not specified' }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Class Dates -->
                                            <div class="col-md-6 mb-4">
                                                <div class="card ee-card">
                                                    <div class="card-header">
                                                        <h2> Class Dates</h2>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Start Date:</strong>
                                                            {{ date('M d, Y', strtotime($program->start_date)) }}</p>
                                                        <p><strong>End Date:</strong>
                                                            {{ date('M d, Y', strtotime($program->end_date)) }}</p>
                                                        <p><strong>No Class Dates:</strong>
                                                            @if (isset($program->no_class_dates) && !empty($program->no_class_dates))
                                                                <div class="mt-2">
                                                                    @foreach (explode(',', $program->no_class_dates) as $date)
                                                                        <span class="badge"
                                                                            style="background-color: #a0c825; color: white; margin: 2px;">
                                                                            {{ $date }}
                                                                        </span>
                                                                    @endforeach
                                                                </div>
                                                            @else
                                                                <span class="text-muted">None specified</span>
                                                            @endif
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Description -->
                                            <div class="col-md-12  mb-4">
                                                <div class="card ee-card">
                                                    <div class="card-header">
                                                        <h2>Description</h2>
                                                    </div>
                                                    <div class="card-body">
                                                        <p>{{ $program->description }}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Cost Information -->
                                            <div class="col-md-12  mb-4">
                                                <div class="card ee-card">
                                                    <div class="card-header">
                                                        <h2>Cost Information</h2>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Enrollment Fee:</strong>
                                                            {{ priceFormat($program->fee) }}</p>
                                                        <p><strong>School Fee:</strong>
                                                            {{ priceFormat($program->school->school_fee) }}</p>
                                                        <p><strong>Platform Fee:</strong>
                                                            {{ priceFormat($program->school->admin_fee) }}</p>
                                                        <p>
                                                            <strong>Total Fee:</strong>
                                                            <span style="color: #a0c825; font-weight: bold;">
                                                                {{ priceFormat($program->fee + $program->school->school_fee + $program->school->admin_fee) }}
                                                            </span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- Enrollment Tab -->
                            <div class="tab-pane" id="Enrollment">
                                <div class="calendar-card">
                                    <div class="calendar-card-head">
                                        <h2>Enrollment</h2>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn-enrollment-outline active"
                                                id="allEnrollmentsBtn">All Enrollments</button>
                                            <button type="button" class="btn-enrollment-outline"
                                                id="todayEnrollmentsBtn">Today's Enrollments</button>
                                        </div>
                                    </div>
                                    <div class="calendar-card-body">
                                        <div id="allEnrollments">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Student Name</th>
                                                            <th>Enrollment Date</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @php
                                                            $enrollments = \App\Models\ProgramStudentEnrollment::where(
                                                                'program_id',
                                                                $program->id,
                                                            )->get();
                                                        @endphp

                                                        @forelse($enrollments as $enrollment)
                                                            <tr>
                                                                <td>
                                                                    <a
                                                                        href="{{ route('school.enrollments.show', encrypt($enrollment->student_id)) }}">
                                                                        {{ $enrollment->student->name }}
                                                                    </a>
                                                                </td>
                                                                <td>{{ date('M d, Y', strtotime($enrollment->created_at)) }}
                                                                </td>
                                                                <td>
                                                                    <span
                                                                        class="badge bg-{{ $enrollment->status == 'Active' ? 'success' : 'warning' }}">
                                                                        {{ $enrollment->status }}
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <a href="{{ route('school.enrollments.show', encrypt($enrollment->student_id)) }}"
                                                                        class="btn btn-sm btn-primary">
                                                                        View Profile
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @empty
                                                            <tr>
                                                                <td colspan="4" class="text-center">No enrollments
                                                                    found</td>
                                                            </tr>
                                                        @endforelse
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <div id="todayEnrollments" style="display: none;">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Student Name</th>
                                                            <th>Enrollment Date</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @php
                                                            $enrollments = \App\Models\ProgramStudentEnrollment::where(
                                                                'program_id',
                                                                $program->id,
                                                            )
                                                                ->whereDate('created_at', now())
                                                                ->get();
                                                        @endphp

                                                        @forelse($enrollments as $enrollment)
                                                            <tr>
                                                                <td>
                                                                    <a
                                                                        href="{{ route('school.enrollments.show', encrypt($enrollment->student_id)) }}">
                                                                        {{ $enrollment->student->name }}
                                                                    </a>
                                                                </td>
                                                                <td>{{ date('M d, Y', strtotime($enrollment->created_at)) }}
                                                                </td>
                                                                <td>
                                                                    <span
                                                                        class="badge bg-{{ $enrollment->status == 'Active' ? 'success' : 'warning' }}">
                                                                        {{ $enrollment->status }}
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <a href="{{ route('school.enrollments.show', encrypt($enrollment->student_id)) }}"
                                                                        class="btn btn-sm btn-primary">
                                                                        View Profile
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @empty
                                                            <tr>
                                                                <td colspan="4" class="text-center">No enrollments
                                                                    found</td>
                                                            </tr>
                                                        @endforelse
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Billing/Transactions Tab -->
                            <div class="tab-pane" id="Billing">
                                <div class="calendar-card">
                                    <div class="calendar-card-head">
                                        <h2>Billing & Transactions</h2>
                                    </div>
                                    <div class="calendar-card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Transaction ID</th>
                                                        <th>Student</th>
                                                        <th>Amount</th>
                                                        <th>Status</th>
                                                        <th>Date</th>
                                                        {{-- <th>Actions</th> --}}
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $transactions = \App\Models\Transaction::whereHas(
                                                            'enrollment',
                                                            function ($q) use ($program) {
                                                                $q->where('program_id', $program->id);
                                                            },
                                                        )->get();
                                                    @endphp

                                                    @forelse($transactions as $transaction)
                                                        <tr>
                                                            <td>{{ $transaction->transaction_id }}</td>
                                                            <td>{{ $transaction->enrollment->student->name }}</td>
                                                            <td>{{ priceFormat($transaction->total) }}</td>
                                                            <td>
                                                                <span
                                                                    class="badge bg-{{ $transaction->status == 'Success' ? 'success' : ($transaction->status == 'Pending' ? 'warning' : 'danger') }}">
                                                                    {{ $transaction->status }}
                                                                </span>
                                                            </td>
                                                            <td>{{ date('M d, Y', strtotime($transaction->created_at)) }}
                                                            </td>
                                                            {{-- <td>
                                                                <a href="#" class="btn btn-sm btn-primary">View
                                                                    Details</a>
                                                            </td> --}}
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="6" class="text-center">No transactions found
                                                            </td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Attendance Tab -->
                            <div class="tab-pane" id="Attendance">
                                <div class="calendar-card">
                                    <div class="ee-head">
                                        <h3>Attendance</h3>
                                        <div class="search-filter wd60">
                                            <div class="row mb-4">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <input type="date" id="attendanceDate" class="form-control"
                                                            value="{{ date('Y-m-d') }}">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <button class="btn-pur wd100" id="loadAttendanceBtn">View
                                                            Attendance</button>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <a href="{{ route('school.program.attendance', $program->id) }}"
                                                            class="btn-pur wd100">Manage Attendance</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="calendar-card-body">


                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Student Name</th>
                                                        <th>Check-in Time</th>
                                                        <th>Check-out Time</th>
                                                        <th>Status</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="attendanceTableBody">
                                                    @php
                                                        $todayLesson = \App\Models\Lesson::whereDate(
                                                            'lesson_date',
                                                            date('Y-m-d'),
                                                        )
                                                            ->whereHas('schedule', function ($q) use ($program) {
                                                                $q->where('program_id', $program->id);
                                                            })
                                                            ->first();

                                                        $attendanceRecords = $todayLesson
                                                            ? \App\Models\ProgramStudentAttendance::where(
                                                                'lesson_id',
                                                                $todayLesson->id,
                                                            )->get()
                                                            : collect([]);

                                                        $enrolledStudents = \App\Models\ProgramStudentEnrollment::where(
                                                            'program_id',
                                                            $program->id,
                                                        )
                                                            ->where('status', 'Active')
                                                            ->get();
                                                    @endphp

                                                    @foreach ($enrolledStudents as $enrollment)
                                                        @php
                                                            $attendance = $attendanceRecords
                                                                ->where('student_id', $enrollment->student_id)
                                                                ->first();
                                                        @endphp
                                                        <tr>
                                                            <td>{{ $enrollment->student->name }}</td>
                                                            <td>{{ $attendance->check_in ?? 'Not checked in' }}</td>
                                                            <td>{{ $attendance->check_out ?? 'Not checked out' }}</td>
                                                            <td>
                                                                @if (!$attendance)
                                                                    <span class="badge bg-danger">Absent</span>
                                                                @elseif($attendance->check_in && $attendance->check_out)
                                                                    <span class="badge bg-success">Present
                                                                        (Complete)
                                                                    </span>
                                                                @elseif($attendance->check_in)
                                                                    <span class="badge bg-warning">Present (Partial)</span>
                                                                @endif
                                                            </td>
                                                            <td>
                                                                <a href="{{ route('school.enrollments.show', encrypt($enrollment->student_id)) }}"
                                                                    class="btn btn-sm btn-primary">
                                                                    View Profile
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    @endforeach

                                                    @if (count($enrolledStudents) == 0)
                                                        <tr>
                                                            <td colspan="5" class="text-center">No students enrolled in
                                                                this class</td>
                                                        </tr>
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- deletepop -->
    <div class="modal ee-modal fade" id="deletepop" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-modal-info">
                        <div class="ee-form-info">
                            <div class="ee-modal-icon">
                                <img src="{{ asset('parents/images/trash1.svg') }}">
                            </div>
                            <h2>Are You Sure?</h2>
                            <p>Do you really want to delete these records? This process cannot be undone.</p>

                            <div class="ee-modal-action">
                                <a class="btn-No" class="btn-close" data-bs-dismiss="modal" aria-label="Close">No</a>

                                <a class="btn-Yes" type="submit" onclick="deletePublic(this)" id="delete_url">Yes</a>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- add A class  -->
    <div class="modal ee-modal fade" id="addaclass" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-form-info">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        <h2>Add A Lesson</h2>
                        <form action="{{ route('school.program-schedules.store') }}" method="post" id="create">
                            @csrf
                            <input type="hidden" name="program_id" value="{{ $program->id }}">
                            <input type="hidden" name="program_schedule_id"
                                value="{{ $program->program_schedule->id }}">

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Lesson Date</label>
                                        <select name="lesson_date" class="form-control select2" required>
                                            <option value="">Select a date</option>
                                            @foreach ($allowed_dates as $date)
                                                <option value="{{ $date }}">{{ date('m-d-Y', strtotime($date)) }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Lesson Name</label>
                                        <input type="text" name="title" class="form-control"
                                            placeholder="Type Here" value="">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Start time</label>
                                        <input type="time" name="start_time" class="form-control"
                                            value="{{ $program->program_schedule->start_time }}" placeholder="Type Here"
                                            value="">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>End time</label>
                                        <input type="time" name="end_time" class="form-control"
                                            value="{{ $program->program_schedule->end_time }}" placeholder="Type Here"
                                            value="">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Details</label>
                                        <textarea name="description" id="" cols="30" rows="5" class="form-control"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button class="cancel-btn" type="button"
                                            data-bs-dismiss="modal"">Cancel</button>
                                        <button class="save-btn" type="submit">Save Detail</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Edit Lession  -->
    <div class="modal ee-modal fade" id="editLesson" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-form-info">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        <h2>Edit Lesson</h2>
                        <form action="{{ route('school.program-schedules.store') }}" method="post" id="edit">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="program_id" value="{{ $program->id }}">
                            <input type="hidden" name="program_schedule_id"
                                value="{{ $program->program_schedule->id }}">

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Lesson Date</label>
                                        <select name="lesson_date" class="form-control select2" id="lesson_date"
                                            required>
                                            <option value="">Select a date</option>
                                            @foreach ($all_allowed_dates as $date)
                                                <option value="{{ $date }}">{{ $date }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Lesson Name</label>
                                        <input type="text" name="title" class="form-control" id="title"
                                            placeholder="Type Here" value="">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Start time</label>
                                        <input type="time" name="start_time" class="form-control" id="start_time"
                                            min="07:00" max="21:00" placeholder="Type Here" value="">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>End time</label>
                                        <input type="time" name="end_time" class="form-control" id="end_time"
                                            min="07:00" max="21:00" placeholder="Type Here" value="">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Details</label>
                                        <textarea name="description" id="description" cols="30" rows="5" class="form-control"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Status</label>
                                        <select name="status" class="form-control" id="status">
                                            <option value="Scheduled">Scheduled</option>
                                            <option value="Cancelled">Cancelled</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button class="cancel-btn" type="button"
                                            data-bs-dismiss="modal"">Cancel</button>
                                        <button class="save-btn" type="submit">Save Detail</button>
                                        <button class="cancel-btn" data-bs-toggle="modal" id="delete_lesson_btn"
                                            type="button"
                                            onclick="document.getElementById('delete_url').setAttribute('data-url', this.getAttribute('data-url'))"
                                            data-bs-target="#deletepop">Delete</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- add A class  -->
    @include('partials.delete_popup')
    <script>
        function deletePublic(ele) {
            var url = ele.getAttribute("data-url");

            var _token = '{{ csrf_token() }}';

            var obj = {

                _token
            };

            $.ajax({
                url: url,
                type: 'DELETE',
                data: obj,
                success: function(data) {
                    // console.log(data);
                    if (data.success) {
                        Swal.fire("Success", data.message, 'success').then((result) => {
                            if (result.value) {
                                var url = $('#redirect_url').val();
                                if (url !== undefined || url != null) {
                                    window.location = url;
                                } else {
                                    location.reload(true);
                                }

                            }
                        });
                    } else {
                        Swal.fire("Error", data.message, 'error').then((result) => {
                            if (result.value) {
                                var url = $('#redirect_url').val();
                                if (url !== undefined || url != null) {
                                    window.location = url;
                                } else {
                                    location.reload(true);
                                }

                            }
                        });;

                        // location.reload(true);

                    }
                }
            });
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');

            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                initialDate: Date.now(),
                validRange: {
                    start: '{{ $program->start_date }}',
                    end: '{{ $program->end_date }}',
                },
                titleFormat: {
                    day: 'numeric',
                    month: 'short'
                },
                slotMinTime: "07:00:00",
                slotMaxTime: "22:00:00",
                allDaySlot: false,
                nowIndicator: true,
                events: @json($lessons_map),
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },

                eventClick: function(info) {
                    // Create a temporary element to hold the data attributes
                    const temp = document.createElement('div');
                    temp.setAttribute('data-title', info.event.title);
                    temp.setAttribute('data-description', info.event.extendedProps.description);
                    temp.setAttribute('data-url', info.event.extendedProps.updateUrl);
                    temp.setAttribute('data-lesson_date', info.event.extendedProps.lesson_date);
                    temp.setAttribute('data-start_time', info.event.extendedProps.start_time);
                    temp.setAttribute('data-end_time', info.event.extendedProps.end_time);
                    temp.setAttribute('data-status', info.event.extendedProps.status);
                    temp.setAttribute('data-delete_url', info.event.extendedProps.delete_url);

                    editLesson(temp); // Call your function with the virtual element
                }
            });

            calendar.render();
        });
    </script>
    <script>
        function editLesson(ele) {
            $("#editLesson").modal("show");
            $("#title").val(ele.getAttribute("data-title"));
            $("#description").val(ele.getAttribute("data-description"));
            $("#lesson_date").val(ele.getAttribute("data-lesson_date"));
            $("#start_time").val(ele.getAttribute("data-start_time"));
            $("#end_time").val(ele.getAttribute("data-end_time"));
            $("#status").val(ele.getAttribute("data-status"));
            $("#delete_lesson_btn").attr('data-url', ele.getAttribute("data-delete_url"));
            $("#edit").attr("action", ele.getAttribute("data-url"));
        }

        function showLoader() {
            document.getElementById('loaderOverlay').style.display = 'block';
            document.getElementById('loader').style.display = 'block';
        }

        function hideLoader() {
            document.getElementById('loaderOverlay').style.display = 'none';
            document.getElementById('loader').style.display = 'none';
        }
        $(document).ready(function() {
            $.validator.addMethod("integer", function(value, ele) {
                return value != 0;
            }, "Please select a valid option");
            $('#create').validate({
                rules: {

                    start_time: {
                        required: true,
                        maxlength: 191,

                    },
                    end_time: {
                        required: true,
                        maxlength: 191,

                    },


                    class_name: {
                        required: true,
                    }
                },
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.addClass("text-danger");
                    element.closest(".form-group").append(error);
                },
                highlight: function(element, errorClass, validClass) {
                    $('.please-wait').click();
                    $(element).addClass("text-danger");
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass("text-danger");
                },
                submitHandler: function(form, event) {
                    event.preventDefault();
                    showLoader();
                    let formData = new FormData(form);

                    $.ajax({
                        type: 'post',
                        url: form.action,
                        data: formData,
                        dataType: 'json',
                        contentType: false,
                        processData: false,

                        success: function(response) {
                            if (response.success) {

                                Swal.fire({
                                    title: 'Success',
                                    text: response.message,
                                    icon: 'success',

                                }).then((result) => {

                                    if (response.redirect == true) {
                                        window.location = response.route;
                                    }
                                    var url = $('#redirect_url').val();
                                    if (url !== undefined || url != null) {
                                        window.location = url;
                                    } else {
                                        location.reload(true);
                                    }
                                })
                                hideLoader();
                                return false;
                            }

                            if (response.success == false) {
                                Swal.fire(
                                    'Error',
                                    response.message,
                                    'error'
                                );
                                hideLoader();

                                return false;
                            }
                        },
                        error: function(data) {
                            if (data.status == 422) {
                                var form = $("#create");
                                let li_htm = '';
                                $.each(data.responseJSON.errors, function(k, v) {
                                    const $input = form.find(
                                        `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                    );
                                    if ($input.next('small').length) {
                                        $input.next('small').html(v);
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').next('small').html(v);
                                        }
                                    } else {
                                        $input.after(
                                            `<small class='text-danger'>${v}</small>`
                                        );
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').after(
                                                `<small class='text-danger'>${v[0]}</small>`
                                            );
                                        }
                                    }
                                    li_htm += `<li>${v}</li>`;
                                });
                                hideLoader();

                                return false;
                            } else {
                                Swal.fire(
                                    'Error',
                                    data.statusText,
                                    'error'
                                );
                            }
                            hideLoader();

                            return false;

                        }
                    });
                }
            })
            $('#edit').validate({
                rules: {




                    title: {
                        required: true,
                    },
                    lesson_date: {
                        required: true,
                    }
                },
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.addClass("text-danger");
                    element.closest(".form-group").append(error);
                },
                highlight: function(element, errorClass, validClass) {
                    $('.please-wait').click();
                    $(element).addClass("text-danger");
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass("text-danger");
                },
                submitHandler: function(form, event) {
                    event.preventDefault();
                    let formData = new FormData(form);

                    $.ajax({
                        type: 'post',
                        url: form.action,
                        data: formData,
                        dataType: 'json',
                        contentType: false,
                        processData: false,

                        success: function(response) {
                            if (response.success) {

                                Swal.fire({
                                    title: 'Success',
                                    text: response.message,
                                    icon: 'success',

                                }).then((result) => {

                                    if (response.redirect == true) {
                                        window.location = response.route;
                                    }
                                    var url = $('#redirect_url').val();
                                    if (url !== undefined || url != null) {
                                        window.location = url;
                                    } else {
                                        location.reload(true);
                                    }
                                })

                                return false;
                            }

                            if (response.success == false) {
                                Swal.fire(
                                    'Error',
                                    response.message,
                                    'error'
                                );

                                return false;
                            }
                        },
                        error: function(data) {
                            if (data.status == 422) {
                                var form = $("#create");
                                let li_htm = '';
                                $.each(data.responseJSON.errors, function(k, v) {
                                    const $input = form.find(
                                        `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                    );
                                    if ($input.next('small').length) {
                                        $input.next('small').html(v);
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').next('small').html(v);
                                        }
                                    } else {
                                        $input.after(
                                            `<small class='text-danger'>${v}</small>`
                                        );
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').after(
                                                `<small class='text-danger'>${v[0]}</small>`
                                            );
                                        }
                                    }
                                    li_htm += `<li>${v}</li>`;
                                });

                                return false;
                            } else {
                                Swal.fire(
                                    'Error',
                                    data.statusText,
                                    'error'
                                );
                            }
                            return false;

                        }
                    });
                }
            })
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            let element = document.getElementById("autofocus");
            if (element) {
                element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "center"
                });
            }
        });
    </script>
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>

    <script>
        // Firebase configuration (Replace with your Firebase project config)
        const firebaseConfig = {
            apiKey: "AIzaSyDRe1eViFofDKYqItv9OcWQUHZPKPdCMJk",
            authDomain: "chat-message-df24c.firebaseapp.com",
            projectId: "chat-message-df24c",
            storageBucket: "chat-message-df24c.firebasestorage.app",
            messagingSenderId: "976425238698",
            appId: "1:976425238698:web:433195679c737d445c90d3",
            measurementId: "G-Y6M5XP5CJS"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();


        // Example User Data (Should be dynamically fetched)
        const currentUserId = "provider_{{ $program->provider->id }}"; // Unique ID of the sender
        const currentUserName = "School Admin"; // Name of the sender
        const currentUserType = "provider"; // "student" or "provider"
        const chatId = "program_schedule_enrollment_{{ $program->id }}"; // Unique group chat ID


        // Function to load messages in real-time
        function loadMessages() {
            db.collection("chats")
                .doc(chatId)
                .collection("messages")
                .orderBy("timestamp")
                .onSnapshot((snapshot) => {
                    const messagesList = document.querySelector(".messages-list");
                    messagesList.innerHTML = ""; // Clear previous messages

                    snapshot.forEach((doc) => {
                        const message = doc.data();
                        const messageClass = message.senderId === currentUserId ? "outgoing-message" : "";
                        const senderName = `${message.senderName}`;

                        messagesList.innerHTML += `
                                                      <div class="message-item ${messageClass}">
                                                          <div class="message-item-chat-card">
                                                              <div class="message-item-user">
                                                                  <img src="{{ asset('parents/images/profile.jpg') }}">
                                                              </div>
                                                              <div class="message-item-chat-content">
                                                                  <div class="message-content"><strong>${senderName}:</strong> ${message.text}</div>
                                                                  <div class="time">${new Date(message.timestamp?.toDate()).toLocaleString()}</div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  `;
                    });

                    // Auto-scroll to the latest message
                    messagesList.scrollTop = messagesList.scrollHeight;
                });
        }


        // Function to send a message
        function sendMessage() {
            console.log('sds');

            const inputField = document.querySelector(".message_text");
            const messageText = inputField.value.trim();
            if (messageText === "") return;

            db.collection("chats")
                .doc(chatId)
                .collection("messages")
                .add({
                    text: messageText,
                    senderId: currentUserId,
                    senderName: currentUserName,
                    senderType: currentUserType,
                    timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                })
                .then(() => {
                    inputField.value = ""; // Clear input after sending
                })
                .catch((error) => console.error("Error sending message: ", error));
        }


        // Attach event listener to send button
        document.querySelector(".btn-send").addEventListener("click", sendMessage);

        // Load messages when the page loads
        document.addEventListener("DOMContentLoaded", loadMessages);
    </script>
    <script>
        // Toggle between all enrollments and today's enrollments
        document.getElementById('allEnrollmentsBtn').addEventListener('click', function() {
            document.getElementById('allEnrollments').style.display = 'block';
            document.getElementById('todayEnrollments').style.display = 'none';
            this.classList.add('active');
            document.getElementById('todayEnrollmentsBtn').classList.remove('active');
        });

        document.getElementById('todayEnrollmentsBtn').addEventListener('click', function() {
            document.getElementById('allEnrollments').style.display = 'none';
            document.getElementById('todayEnrollments').style.display = 'block';
            this.classList.add('active');
            document.getElementById('allEnrollmentsBtn').classList.remove('active');
        });

        // Load attendance for selected date
        document.getElementById('loadAttendanceBtn').addEventListener('click', function() {
            const selectedDate = document.getElementById('attendanceDate').value;
            showLoader();

            // AJAX request to load attendance for the selected date
            $.ajax({
                url: "{{ route('school.programs.get_attendance', $program->id) }}",
                type: "GET",
                data: {
                    date: selectedDate
                },
                success: function(response) {
                    document.getElementById('attendanceTableBody').innerHTML = response;
                    hideLoader();
                },
                error: function() {
                    hideLoader();
                    // alert('Failed to load attendance data');
                    Swal.fire("Error", "Failed to load attendance data", "error");
                }
            });
        });

        function ApproveChanges() {
            Swal.fire({
                title: 'Warning',
                text: "Do you want to approve changes?",
                icon: 'warning',

            }).then((result) => {
                if (result.value) {
                    showLoader();
                    $.post("{{ route('school.program.approve_changes', $program->id) }}", {
                            _token: "{{ csrf_token() }}"
                        },
                        function(data) {
                            hideLoader();
                            if (data.success) {
                                Swal.fire("Success", data.message, "success").then((data) => {
                                    if (data.value) {
                                        location.reload();
                                    }
                                })
                            } else {
                                Swal.fire("Error", data.message, "error");
                            }
                        }
                    );
                }
            });
        }
    </script>
@endsection
