@extends('layouts.schools.app')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('parents/css/home.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/program.css') }}">
@endpush
@section('content')
    <div class="body-main-content">
        <div class="programs-section">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('school.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('school.programs.index') }}">Classes</a></li>

                        <li class="breadcrumb-item active" aria-current="page">{{ isset($program) ? 'Edit' : 'Create' }}
                            Class
                        </li>
                    </ol>
                </nav>
                <div class="ee-head" style="justify-content: start">
                    <div class="search-filter px-2">
                        <div class="row g-2">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <a class="btn-gr wd100" href="{{ route('school.programs.index') }}"><img
                                            src="http://18.226.133.171/public/images/back.svg" height="25"
                                            alt=""></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h3>{{ isset($program) ? 'Create' : 'Edit' }} New Class</h3>

                </div>

                <div class="ee-content">
                    <form
                        action="{{ isset($program) ? route('school.programs.update', $program) : route('school.programs.store') }}"
                        method="post" id="create">
                        @csrf
                        @if (isset($program))
                            @method('PUT')
                        @endif
                        <input type="hidden" name="" id="redirect_url" value="{{ route('school.programs.index') }}">

                        <div class="row g-2">
                            <div class="col-md-12">
                                <div class="manage-create-card">
                                    <div class="ee-form-info">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Class Title</label>
                                                    <input type="text" name="title" class="form-control" required
                                                        @if (isset($program)) value="{{ $program->title }}" @endif
                                                        placeholder="Class Title">
                                                </div>
                                            </div>
                                            <input type="hidden" id="redirect_url"
                                                value="{{ route('school.programs.index') }}">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Class Fee($)</label>
                                                    <input type="number" name="fee" class="form-control" required
                                                        @if (isset($program)) value="{{ $program->fee }}" @endif
                                                        placeholder="$0.00" min="0">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Sessions</label>
                                                    <select class="form-control" name="session">

                                                        @foreach ($sessions as $item)
                                                            <option value="{{ $item->name }}"
                                                                @selected(isset($program) && $item->name == $program->session)>{{ $item->name }}
                                                            </option>
                                                        @endforeach

                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Service Provider</label>
                                                    <select class="form-control " name="service_provider_id"
                                                        id="provider_id">
                                                        <option value="0">Select</option>
                                                        @foreach ($providers as $item)
                                                            <option value="{{ $item->id }}"
                                                                @selected(isset($program) && $item->id == $program->service_provider_id)>{{ $item->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="instructor_id" class="form-label">Instructor</label>
                                                    <select class="form-control" id="instructor_id" name="instructor_id"
                                                        >
                                                        <option value="">Select Instructor</option>
                                                        @if (isset($program) && isset($programInstructor))
                                                            @foreach ($instructors as $item)
                                                                <option value="{{ $item->id }}"
                                                                    @selected(isset($program) && $item->id == $programInstructor->instructor_id)>
                                                                    {{ $item->name }}</option>
                                                            @endforeach
                                                        @endif
                                                        <!-- Instructors will be loaded dynamically based on provider selection -->
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Grades</label>
                                                    <select class="form-control select2" name="grades[]" multiple>
                                                        <option value="Kindergarten" @selected(isset($program) && in_array('Kindergarten', explode(',', $program->grades ?? '')))>
                                                            Kindergarten</option>
                                                        @foreach (range(1, 12) as $grade)
                                                            <option value="Grade {{ $grade }}"
                                                                @selected(isset($program) && in_array('Grade ' . $grade, explode(',', $program->grades ?? '')))>Grade {{ $grade }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Location</label>
                                                    <input type="text" name="location" class="form-control"
                                                        @if (isset($program)) value="{{ $program->location }}" @endif
                                                        placeholder="Class Location">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Min Students</label>
                                                    <input type="number" name="min_students" class="form-control"
                                                        min="1"
                                                        @if (isset($program)) value="{{ $program->min_students }}" @endif
                                                        placeholder="Minimum Students">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Max Students</label>
                                                    <input type="number" name="max_students" class="form-control"
                                                        min="1"
                                                        @if (isset($program)) value="{{ $program->max_students }}" @endif
                                                        placeholder="Maximum Students">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Start Date</label>
                                                    <div class="input-group calendar-input-group">
                                                        <input type="text" class="form-control" name="start_date"
                                                            id="start_date"
                                                            @if (isset($program)) value="{{ date('m-d-Y', strtotime($program->start_date)) }}" @endif
                                                            placeholder="MM-DD-YYYY" readonly><span
                                                            class="input-group-addon calendar-icon-info"
                                                            id="datepicker-icon">
                                                            <!-- SVG calendar image -->
                                                            <svg width="20" height="25" viewBox="0 0 12 14"
                                                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M9 1V3M3 1V3M0.5 5H11.5M1 2H11C11.2761 2 11.5 2.22386 11.5 2.5V12.5C11.5 12.7761 11.2761 13 11 13H1C0.723858 13 0.5 12.7761 0.5 12.5V2.5C0.5 2.22386 0.723858 2 1 2Z"
                                                                    stroke="#4A4A4B" stroke-linecap="round"
                                                                    stroke-linejoin="round" />
                                                            </svg>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>End Date</label>
                                                    <div class="form-group">
                                                        {{-- <input class="form-control" name="date" onchange="changeDate(this.value)"
                                                                @if (request()->has('date')) value="{{ request('date') }}" @endif
                                                                type="date" /> --}}
                                                        <div class="input-group calendar-input-group">
                                                            <input type="text" class="form-control" name="end_date"
                                                                id="end_date"
                                                                @if (isset($program)) value="{{ date('m-d-Y', strtotime($program->end_date)) }}" @endif
                                                                placeholder="MM-DD-YYYY" readonly><span
                                                                class="input-group-addon calendar-icon-info"
                                                                id="datepicker-icon">
                                                                <!-- SVG calendar image -->
                                                                <svg width="20" height="25" viewBox="0 0 12 14"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M9 1V3M3 1V3M0.5 5H11.5M1 2H11C11.2761 2 11.5 2.22386 11.5 2.5V12.5C11.5 12.7761 11.2761 13 11 13H1C0.723858 13 0.5 12.7761 0.5 12.5V2.5C0.5 2.22386 0.723858 2 1 2Z"
                                                                        stroke="#4A4A4B" stroke-linecap="round"
                                                                        stroke-linejoin="round" />
                                                                </svg>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @php
                                                $schedule =
                                                    isset($program) && isset($program->program_schedule)
                                                        ? $program->program_schedule
                                                        : null;
                                                $daysArray = $schedule ? explode(',', $schedule->days) : [];
                                                $allDays = [
                                                    'Sunday',
                                                    'Monday',
                                                    'Tuesday',
                                                    'Wednesday',
                                                    'Thursday',
                                                    'Friday',
                                                    'Saturday',
                                                ];
                                                $isDaily = count(array_intersect($daysArray, $allDays)) === 7;
                                            @endphp

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Start Time</label>
                                                    <input type="time" name="start_time" class="form-control"
                                                        value="{{ $schedule ? date('H:i', strtotime($schedule->start_time)) : '' }}">
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>End Time</label>
                                                    <input type="time" name="end_time" class="form-control"
                                                        value="{{ $schedule ? date('H:i', strtotime($schedule->end_time)) : '' }}">
                                                </div>
                                            </div>
  <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>Status</label>
                                                    <select class="form-control" name="status">
                                                        <option value="Active" {{ old('status', $program->status ?? 'Active') == 'Active' ? 'selected' : '' }}>Active</option>
                                                        <option value="Inactive" {{ old('status', $program->status ?? 'Active') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                                        <option value="Upcoming" {{ old('status', $program->status ?? 'Active') == 'Upcoming' ? 'selected' : '' }}>Send for provider review</option>
                                                  
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label> Days</label>
                                                    <div class="day-form-list">
                                                        <div class="days-list-item">
                                                            <div class="daycheckbox">
                                                                <input type="checkbox" id="Daily" name="daily"
                                                                    @checked($isDaily)>
                                                                <label for="Daily">
                                                                    <span class="checkbox-text">Daily</span>
                                                                </label>
                                                            </div>
                                                        </div>

                                                        @foreach ($allDays as $day)
                                                            <div class="days-list-item">
                                                                <div class="daycheckbox">
                                                                    <input type="checkbox" id="{{ substr($day, 0, 3) }}_"
                                                                        value="{{ $day }}" name="days[]"
                                                                        @checked(in_array($day, $daysArray))>
                                                                    <label for="{{ substr($day, 0, 3) }}_">
                                                                        <span
                                                                            class="checkbox-text">{{ substr($day, 0, 3) }}</span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>








                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label>Class Description</label>
                                                    <textarea class="form-control" name="description" placeholder="Class Description"> @if (isset($program))
{{ $program->description }}
@endif
</textarea>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label>Supplies</label>
                                                    <textarea class="form-control" name="supplies" placeholder="List of supplies needed for this class">
@if (isset($program))
{{ $program->supplies }}
@endif
</textarea>
                                                    <small class="text-muted">Enter each supply item on a new line</small>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label>Upload Photo</label>
                                                    <input type="file" class="form-control" name="image"
                                                        @if (isset($program)) @else
                                                required @endif
                                                        accept="image/*" placeholder="">
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <button class="cancel-btn"
                                                        onclick="location.replace('{{ isset($program) ? route('school.programs.show', $program->id) : route('school.programs.index') }}')"
                                                        type="button" data-bs-dismiss="modal">Cancel</button>
                                                    <button class="save-btn">
                                                        @if (isset($program))
                                                            Update Class
                                                        @else
                                                            Create New Class
                                                        @endif
                                                    </button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function() {
            // Initialize select2 for dropdowns
            $('.select2').select2({
                placeholder: "Select option(s)",
                allowClear: true
            });

            $.validator.addMethod("integer", function(value, ele) {
                return value != 0;
            }, "Please select a valid option");

            $.validator.addMethod("minMaxCheck", function(value, element) {
                var minStudents = parseInt($('input[name="min_students"]').val()) || 0;
                var maxStudents = parseInt(value) || 0;
                return maxStudents === 0 || maxStudents >= minStudents;
            }, "Maximum students must be greater than or equal to minimum students");

            $('#create').validate({
                rules: {
                    title: {
                        required: true,
                        maxlength: 191,
                    },
                    start_date: {
                        required: true,
                        maxlength: 191,
                    },
                    end_date: {
                        required: true,
                        maxlength: 191,
                    },
                    service_provider_id: {
                        required: true,
                        integer: true
                    },
                    fee: {
                        required: true,
                        integer: true
                    },
                    min_students: {
                        number: true,
                        min: 1
                    },
                    max_students: {
                        number: true,
                        min: 1,
                        minMaxCheck: true
                    }
                },
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.addClass("text-danger");
                    element.closest(".form-group").append(error);
                },
                highlight: function(element, errorClass, validClass) {
                    $('.please-wait').click();
                    $(element).addClass("text-danger");
                },
                unhighlight: function(element, errorClass, validClass) {
                    $(element).removeClass("text-danger");
                },
                submitHandler: function(form, event) {
                    event.preventDefault();
                    let formData = new FormData(form);

                    $.ajax({
                        type: 'post',
                        url: form.action,
                        data: formData,
                        dataType: 'json',
                        contentType: false,
                        processData: false,

                        success: function(response) {
                            if (response.success) {

                                Swal.fire({
                                    title: 'Success',
                                    text: response.message,
                                    icon: 'success',

                                }).then((result) => {

                                    if (response.redirect == true) {
                                        window.location = response.route;
                                    }
                                    var url = $('#redirect_url').val();
                                    if (url !== undefined || url != null) {
                                        window.location = url;
                                    } else {
                                        location.reload(true);
                                    }
                                })

                                return false;
                            }

                            if (response.success == false) {
                                Swal.fire(
                                    'Error',
                                    response.message,
                                    'error'
                                );

                                return false;
                            }
                        },
                        error: function(data) {
                            if (data.status == 422) {
                                var form = $("#create");
                                let li_htm = '';
                                $.each(data.responseJSON.errors, function(k, v) {
                                    const $input = form.find(
                                        `input[name=${k}],select[name=${k}],textarea[name=${k}]`
                                    );
                                    if ($input.next('small').length) {
                                        $input.next('small').html(v);
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').next('small').html(v);
                                        }
                                    } else {
                                        $input.after(
                                            `<small class='text-danger'>${v}</small>`
                                        );
                                        if (k == 'services' || k == 'membership') {
                                            $('#myselect').after(
                                                `<small class='text-danger'>${v[0]}</small>`
                                            );
                                        }
                                    }
                                    li_htm += `<li>${v}</li>`;
                                });

                                return false;
                            } else {
                                Swal.fire(
                                    'Error',
                                    data.statusText,
                                    'error'
                                );
                            }
                            return false;

                        }
                    });
                }
            })

        });
        document.getElementById("Daily").addEventListener("change", function() {
            const checked = this.checked;
            const checkboxes = document.querySelectorAll('input[name="days[]"]');
            checkboxes.forEach((cb) => (cb.checked = checked));
        });
    </script>
    <script>
        $(document).ready(function() {

            if ($("#start_date")) {
                $("#start_date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    // yearRange: 'c:c+100', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
            if ($("#end_date")) {
                $("#end_date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    minDate: 0, // Maximum selectable start_date is today
                    changeMonth: true,
                    changeYear: true,
                    // yearRange: 'c:c+100', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);


                    }
                });
            }
        })
    </script>
        <script>
        // Load instructors based on provider selection
        document.getElementById('provider_id').addEventListener('change', function() {
            const providerId = this.value;
            if (providerId > 0) {
                const instructorSelect = document.getElementById('instructor_id');

                // Clear current options
                instructorSelect.innerHTML = '<option value="">Select Instructor</option>';

                if (providerId) {
                    // Fetch instructors for the selected provider
                    fetch(`{{route('admin.providers.instructors.get') }}?id=${providerId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(instructor => {
                                const option = document.createElement('option');
                                option.value = instructor.id;
                                option.textContent = instructor.name;
                                instructorSelect.appendChild(option);
                            });
                        })
                        .catch(error => console.error('Error fetching instructors:', error));
                }
            }

        });
    </script>
@endsection
