@extends('layouts.schools.app')
@push('css')
    <link rel="stylesheet" type="text/css" href="{{ asset('providers/css/classes.css') }}">
@endpush
@section('content')
    <div class="body-main-content">
        <div class="programs-section">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('school.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Classes </li>

                    </ol>
                </nav>
                <div class="ee-head">
                    <h3>Manage Classes</h3>
                    <div class="search-filter wd80">
                        <div class="row g-2">
                            <div class="col-md-2">
                                <div class="form-group">

                                    <select class="form-control" onchange="ChangeSeason(this.value)">
                                        <option value="">Select Session</option>
                                        @foreach ($sessions as $session)
                                            <option value="{{ $session->name }}" @selected(request()->has('session') && request('session') == $session->name)>
                                                {{ $session->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <script>
                                        function ChangeSeason(val) {
                                            var currentUrl = new URL(window.location.href);
                                            // Add or update the 'run_id' parameter
                                            currentUrl.searchParams.set('session', val);
                                            if (val == "") {
                                                currentUrl.searchParams.delete('session');

                                            }
                                            // Reload the page with the new URL
                                            window.location.href = currentUrl.toString();
                                        }
                                    </script>
                                </div>
                            </div>

                            <div class="col-md-2">
                                @include('partials.days_filter')

                            </div>

                            <div class="col-md-2">
                                @include('partials.status_filter')
                            </div>
                            <div class="col-md-1">
                                @include('partials.reset')
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <a class="btn-gr wd100" href="{{ route('school.manage_sessions') }}">Manage Sessions</a>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <a class="btn-pur wd100" href="{{ route('school.programs.create') }}">Add</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ee-content">
                    <div class="row g-2">


                        @forelse ($programs as $item)
                            <div class="col-md-12">
                                <div class="class-item-card">
                                    <div class="class-item-image">
                                        <img
                                            src="{{ $item->image ? asset('uploads/programs/' . $item->image) : asset('images/no.svg') }}">
                                    </div>
                                    <div class="class-item-content">
                                        <h2>{{ $item->title }}

                                        </h2>
                                        <div
                                            class="my-2 d-inline p-1 alert alert-{{ $item->status == 'Active' ? 'success' : 'warning' }}">
                                            {{ $item->status }}
                                        </div>
                                        <div class="programs-KID">{{ $item->total ?? 0 }} KID Enrolled</div>
                                        @php
                                            $selectedDays = explode(',', $item->program_schedule->days); // Convert "Monday,Tuesday" into an array
                                            $days = [
                                                'Sunday' => 'S',
                                                'Monday' => 'M',
                                                'Tuesday' => 'T',
                                                'Wednesday' => 'W',
                                                'Thursday' => 'T',
                                                'Friday' => 'F',
                                                'Saturday' => 'S',
                                            ];
                                        @endphp

                                        <div class="programs-day-list">
                                            @foreach ($days as $day => $short)
                                                <div
                                                    class="day-item {{ in_array($day, $selectedDays) ? 'day-active' : '' }}">
                                                    {{ $short }} </div>
                                            @endforeach
                                        </div>
                                        <div class="bg-light">Provider: {{ $item->provider->name }}</div>
                                        <p>{{ substr($item->description, 0, 200) }}... @if (strlen($item->description) > 200)
                                                <span class="text-primary" style="cursor:pointer"
                                                    onclick="readMore('{{ $item->description }}')">read more</span>
                                            @endif
                                        </p>

                                        <div class="programs-date-list">
                                            <div class="programs-date-card">
                                                <div class="programs-date-icon">
                                                    <img src="{{ asset('providers/images/calendar.svg') }}">
                                                </div>
                                                <div class="programs-date-text">
                                                    <h4>Start Date: </h4>
                                                    <h5> {{ date('M d, Y', strtotime($item->start_date)) }}</h5>
                                                </div>
                                            </div>

                                            <div class="programs-date-card">
                                                <div class="programs-date-icon">
                                                    <img src="{{ asset('providers/images/calendar.svg') }}">
                                                </div>
                                                <div class="programs-date-text">
                                                    <h4>End Date</h4>
                                                    <h5> {{ date('M d, Y', strtotime($item->end_date)) }}</h5>
                                                </div>
                                            </div>
                                        </div>



                                    </div>
                                    <div class="programs-action">
                                        <a class="btn-outline-pur w-100"
                                            href="{{ route('school.programs.show', encrypt($item->id)) }}">View
                                            Detail</a>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div style="display: flex">
                                <img src="{{ asset('images/no-records.svg') }}" style="height: 400px;margin:auto"
                                    alt="">

                            </div>
                        @endforelse

                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('partials.read_more')
@endsection
