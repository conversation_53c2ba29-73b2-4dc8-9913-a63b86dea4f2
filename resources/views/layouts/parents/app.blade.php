<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Dashboard | Everything Enrichment</title>
    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('parents/css/header-footer.css') }}">

    <script src="{{ asset('parents/js/jquery-3.7.1.min.js') }}" type="text/javascript"></script>

    <script src="{{ asset('plugins/bootstrap/js/bootstrap.bundle.min.js') }}" type="text/javascript"></script>

    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/select2/select2.min.css') }}">

    <script src="{{ asset('plugins/select2/select2.min.js') }}" type="text/javascript"></script>
    <script src="{{ asset('parents/js/function.js') }}" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('plugins/jquery-validation/jquery.validate.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/3.3.4/jquery.inputmask.bundle.min.js"></script>
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <style>
        .calendar-input-group {
            position: relative;
        }

        .fc-button {
            text-transform: uppercase;
        }

        .calendar-icon-info {
            position: absolute;
            right: 10px;
            top: 10px;
        }

        .calendar-input-group input::placeholder {
            color: gray;
            opacity: 1;
            /* Firefox */
        }

        .calendar-input-group span {
            color: white;
        }

        .fc-button {
            text-transform: uppercase !Important;
        }
    </style>
    @stack('css')
</head>

<body>
    <header class="header">
        <div class="container">
            <div class="row gx-0 align-items-center">
                <div class="col-xl-2 col-lg-2 col-md-2 col-2">
                    <div class="ee-logo">
                        <a href="#"><img src="{{ asset('parents/images/logo.png') }}" alt=""></a>
                    </div>
                </div>
                <div class="col-xl-8 col-lg-8 col-md-8 col-8">
                    <div class="ee-header-box d-flex  justify-content-end">
                        <div class="ee-main-menu pl-45 d-none d-xl-block">
                            <nav class="ee-mobile-menu-active">
                                <ul>
                                    <li>
                                        <a href="{{ route('parent.dashboard') }}"
                                            class="{{ request()->routeIs('parent.dashboard') ? 'active' : '' }}">
                                            All Classes
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('parent.enrolled.history') }}"
                                            class="{{ request()->routeIs('parent.enrolled.history') ? 'active' : '' }}">
                                            Enrollment History
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('parent.messages.index') }}"
                                            class="{{ request()->routeIs('parent.messages*') ? 'active' : '' }}">
                                            Messages
                                        </a>
                                    </li>
                                     {{-- <li>
                                        <a href="{{ route('parent.kids.index') }}"
                                            class="{{ request()->routeIs('parent.kids.index') ? 'active' : '' }}">
                                           Kids
                                        </a>
                                    </li> --}}
                                    <li>
                                        <a href="{{ route('parent.profile') }}"
                                            class="{{ request()->routeIs('parent.profile') ? 'active' : '' }}">
                                            My Profile
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>

                        <div class="ee-header-bar d-lg-none">
                            <button class="ee-offcanvas-toogle"><img
                                    src="{{ asset('parents/images/nav.svg') }}"></button>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-lg-2 col-md-2 col-2">
                    <nav class="ee-logout-account">
                        <ul>
                            <li><a class="logout-btn" style="cursor: pointer" onclick="askLogout()">Logout</a></li>
                            <li style="position: relative;">
                                <a href="{{ route('parent.cart') }}" class="cart-btn">
                                    <img src="{{ asset('parents/images/shopping-cart.svg') }}">
                                    @if (cartCount())
                                        <span class="cart-count">{{ cartCount() }}</span>
                                        <!-- Updat        e the count dynamically -->
                                    @endif
                                </a>
                            </li>


                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </header>
    @yield('content')

    <!-- logout -->
    <div class="modal ee-modal fade" id="logoutpop" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-modal-info">
                        <div class="ee-form-info">
                            <div class="ee-modal-icon">
                                <img src="{{ asset('parents/images/question-circle.svg') }}">
                            </div>
                            <h2>Are You Sure?</h2>
                            <p>Do you really want to logout?</p>
                            <div class="ee-modal-action">
                                <a class="btn-No" class="btn-close" style="cursor: pointer" data-bs-dismiss="modal"
                                    aria-label="Close">No</a>
                                <a class="btn-Yes"href="{{ route('parent.logout') }}">Yes</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {

            $('.select2').select2(); // Initialize Select2

            $(":input").inputmask();
        });
    </script>
    <script>
        function askLogout() {
            $("#logoutpop").modal("show");
            // Swal.fire({
            //     title: '',
            //     text: "Are you sure you want to logout?",
            //     iconHtml: '<img src="{{ asset('images/logo.png') }}" style="background: #141010;height: 80px;border-radius: 36px;">',
            //     customClass: {
            //         icon: 'no-border'
            //     },
            //     type: 'warning',
            //     showCancelButton: true,
            //     confirmButtonColor: '#3085d6',
            //     cancelButtonColor: '#d33',
            //     confirmButtonText: 'Yes'
            // }).then((result) => {
            //     if (result.value) {
            //         location.replace("{{ route('parent.logout') }}")
            //         swal(
            //             'Logout',
            //             'Yo are logged out successfully.',
            //             'success'
            //         )
            //     }
            // })
        }
    </script>
    <script>
        $(document).ready(function() {

            if ($("#date")) {
                $("#date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    maxDate: 0, // Maximum selectable date is today
                    changeMonth: true,
                    changeYear: true,
                    yearRange: 'c-100:c', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);
                        if ($("#date").attr("name") == "date") {
                            var dateParts = dateText.split('-');
                            var selectedValue = dateParts[2] + '-' + dateParts[0] + '-' + dateParts[1];

                            var currentUrl = new URL(window.location.href);
                            // Add or update the 'run_id' parameter
                            currentUrl.searchParams.set('date', selectedValue);
                            if (selectedValue == "") {
                                currentUrl.searchParams.delete('date');

                            }
                            // Reload the page with the new URL
                            window.location.href = currentUrl.toString();
                        }

                    }
                });
            }
            if ($("#date_future")) {
                $("#date_future").datepicker({
                    dateFormat: 'mm-dd-yy',
                    maxDate: 0, // Maximum selectable date_future is today
                    changeMonth: true,
                    changeYear: true,
                    yearRange: 'c:c-100', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);
                        if ($("#date_future").attr("name") == "date") {
                            var dateParts = dateText.split('-');
                            var selectedValue = dateParts[2] + '-' + dateParts[0] + '-' + dateParts[1];

                            var currentUrl = new URL(window.location.href);
                            // Add or update the 'run_id' parameter
                            currentUrl.searchParams.set('date', selectedValue);
                            if (selectedValue == "") {
                                currentUrl.searchParams.delete('date');

                            }
                            // Reload the page with the new URL
                            window.location.href = currentUrl.toString();
                        }

                    }
                });
            }
        })
    </script>
    @if (Session::has('success'))
        <script>
            Swal.fire('Success', '{{ Session('success') }}', 'success');
        </script>
    @endif

    @if (Session::has('error'))
        <script>
            Swal.fire('Error', '{{ Session('error') }}', 'error');
        </script>
    @endif
    @stack('js')
</body>

</html>
