<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Provider | Everything Enrichment</title>


    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/bootstrap/css/bootstrap.min.css') }}">


    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/header-footer.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('schools/css/responsive.css') }}">


    <script src="{{ asset('schools/js/jquery-3.7.1.min.js') }}" type="text/javascript"></script>

    <script src="{{ asset('plugins/bootstrap/js/bootstrap.bundle.min.js') }}" type="text/javascript"></script>

    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/select2/select2.min.css') }}">

    <script src="{{ asset('plugins/select2/select2.min.js') }}" type="text/javascript"></script>
    {{-- <script src="{{ asset('schools/js/function.js') }}" type="text/javascript"></script> --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('plugins/jquery-validation/jquery.validate.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/3.3.4/jquery.inputmask.bundle.min.js"></script>
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <style>
        .calendar-input-group {
            position: relative;
        }

        .calendar-icon-info {
            position: absolute;
            right: 10px;
            top: 10px;
        }

        .calendar-input-group input::placeholder {
            color: gray;
            opacity: 1;
            /* Firefox */
        }

        .calendar-input-group span {
            color: white;
        }

        .fc-button {
            text-transform: uppercase !Important;
        }
    </style>
    @stack('css')


    <style>
        .btn-primary {
            background-color: #6f51bd !important;
        }
    </style>
</head>

<body class="main-site ccj-panel">
    <div class="page-body-wrapper">
        <div class="sidebar-wrapper sidebar-offcanvas" id="sidebar">
            <div class="sidebar-logo">
                <a class="brand-logo" href="{{ route('service_provider.dashboard') }}">
                    <img class="" src="{{ asset('schools/images/logo.png') }}" alt="">
                </a>
                <!--<a class="brand-logo-mini" href="index.html">-->
                <!--    <img class="" src="{{ asset('schools/images/logo-icon.svg') }}" alt="">-->
                <!--</a>-->
            </div>
            <div class="sidebar-nav">
                <nav class="sidebar">
                    <ul class="nav">
                        <li class="nav-item  @if (Route::is('service_provider.dashboard')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.dashboard') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M10.5 19.9V4.1C10.5 2.6 9.86 2 8.27 2H4.23C2.64 2 2 2.6 2 4.1V19.9C2 21.4 2.64 22 4.23 22H8.27C9.86 22 10.5 21.4 10.5 19.9Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M22 10.9V4.1C22 2.6 21.36 2 19.77 2H15.73C14.14 2 13.5 2.6 13.5 4.1V10.9C13.5 12.4 14.14 13 15.73 13H19.77C21.36 13 22 12.4 22 10.9Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M22 19.9V18.1C22 16.6 21.36 16 19.77 16H15.73C14.14 16 13.5 16.6 13.5 18.1V19.9C13.5 21.4 14.14 22 15.73 22H19.77C21.36 22 22 21.4 22 19.9Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item @if (Route::is('service_provider.setup*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.setup.index') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M9.25 22.25H14.75C19.5 22.25 21.25 20.5 21.25 15.75V8.25C21.25 3.5 19.5 1.75 14.75 1.75H9.25C4.5 1.75 2.75 3.5 2.75 8.25V15.75C2.75 20.5 4.5 22.25 9.25 22.25Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M12 7V17M7 12H17" stroke="white" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Setup Guide</span>
                            </a>
                        </li>

                        <li class="nav-item  @if (Route::is('service_provider.classes*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.classes') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path
                                            d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Manage Classes</span>
                            </a>
                        </li>

                        <li class="nav-item   @if (Route::is('service_provider.attendance*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.attendance') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M9 2H15C20 2 22 4 22 9V15C22 20 20 22 15 22H9C4 22 2 20 2 15V9C2 4 4 2 9 2Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M8 10.5H16" stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M8 15.5H13.5" stroke="white" stroke-width="1.5"
                                            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Attendance</span>
                            </a>
                        </li>

                        <li class="nav-item   @if (Route::is('service_provider.class.catalogue*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.class.catalogue') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M16 2H8C4 2 2 4 2 8V16C2 20 4 22 8 22H16C20 22 22 20 22 16V8C22 4 20 2 16 2Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M7 10.5H17M7 15.5H14" stroke="white" stroke-width="1.5"
                                            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Class Catalogue</span>
                            </a>
                        </li>

                        <li class="nav-item   @if (Route::is('service_provider.instructors*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.instructors.index') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M12.12 12.78C12.05 12.77 11.96 12.77 11.88 12.78C10.12 12.72 8.71997 11.28 8.71997 9.51C8.71997 7.7 10.18 6.23 12 6.23C13.81 6.23 15.28 7.7 15.28 9.51C15.27 11.28 13.88 12.72 12.12 12.78Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M18.74 19.38C16.96 21.01 14.6 22 12 22C9.40001 22 7.04001 21.01 5.26001 19.38C5.36001 18.44 5.96001 17.52 7.03001 16.8C9.77001 14.98 14.25 14.98 16.97 16.8C18.04 17.52 18.64 18.44 18.74 19.38Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Instructors</span>
                            </a>
                        </li>

                        <li class="nav-item   @if (Route::is('service_provider.reports*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.reports') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M21 7V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V7C3 4 4.5 2 8 2H16C19.5 2 21 4 21 7Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M14.5 4.5V6.5C14.5 7.6 15.4 8.5 16.5 8.5H18.5" stroke="white"
                                            stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M8 13H12M8 17H16" stroke="white" stroke-width="1.5"
                                            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Reports</span>
                            </a>
                        </li>
                        <li class="nav-item   @if (Route::is('service_provider.my_schools')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.my_schools') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M7 8H17M7 13H13" stroke="white" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">My Schools</span>
                            </a>
                        </li>
                        <li class="nav-item   @if (Route::is('service_provider.network')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.network') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M7 8H17M7 13H13" stroke="white" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Network</span>
                            </a>
                        </li>
                        <li class="nav-item   @if (Route::is('service_provider.messages*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.messages.index') }}">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z"
                                            stroke="white" stroke-width="1.5" stroke-miterlimit="10"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M7 8H17M7 13H13" stroke="white" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Messages</span>
                            </a>
                        </li>
                        <li class="nav-item          @if (Route::is('service_provider.profile*')) active @endif">
                            <a class="nav-link" href="{{ route('service_provider.profile') }}">
                                <span class="menu-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M12.12 12.7805C12.05 12.7705 11.96 12.7705 11.88 12.7805C10.12 12.7205 8.71997 11.2805 8.71997 9.51047C8.71997 7.70047 10.18 6.23047 12 6.23047C13.81 6.23047 15.28 7.70047 15.28 9.51047C15.27 11.2805 13.88 12.7205 12.12 12.7805Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M18.74 19.3796C16.96 21.0096 14.6 21.9996 12 21.9996C9.40001 21.9996 7.04001 21.0096 5.26001 19.3796C5.36001 18.4396 5.96001 17.5196 7.03001 16.7996C9.77001 14.9796 14.25 14.9796 16.97 16.7996C18.04 17.5196 18.64 18.4396 18.74 19.3796Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                            stroke="white" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title"> Profile</span>
                            </a>
                        </li>



                        <li class="nav-item">
                            <a class="nav-link" onclick="askLogout()" style="cursor: pointer">
                                <span class="menu-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path d="M9.32001 6.5L11.88 3.94L14.44 6.5" stroke="white" stroke-width="1.5"
                                            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M11.88 14.18V4.01" stroke="white" stroke-width="1.5"
                                            stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M4 12C4 16.42 7 20 12 20C17 20 20 16.42 20 12" stroke="white"
                                            stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="menu-title">Logout</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        <div class="body-wrapper">
            <div class="header">
                <nav class="navbar">
                    <div class="navbar-menu-wrapper">
                        <ul class="navbar-nav f-navbar-nav">
                            <!-- <li class="nav-item">
                                <a class="nav-link nav-toggler" data-toggle="minimize">
                                   <img src="{{ asset('schools/images/menu-icon.svg') }}">
                                </a>
                            </li> -->
                            <li class="nav-item">
                                <div class="breadcrumb-title">
                                    {{ isset($title) ? $title : 'Dashboard' }}
                                </div>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <li class="nav-item noti-dropdown dropdown">
                                {{-- <a class="nav-link  dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="noti-icon">
                                        <img src="{{ asset('schools/images/notification-bing.svg') }}"
                                            alt="user">
                                        <span class="noti-badge">3</span>
                                    </div>
                                </a> --}}
                                <div class="dropdown-menu">
                                    <div class="noti-item-heading">
                                        <h2>Notification (2)</h2>
                                        <a href="#!" class="markallbtn">Mark All as read</a>
                                    </div>
                                    <div class="noti-item-list">
                                        <div class="noti-item">
                                            <div class="noti-item-icon"><img
                                                    src="{{ asset('schools/images/notification-bing.svg') }}"
                                                    alt="user"></div>
                                            <div class="noti-item-text">
                                                <p>Well done! You successfullyread this important.</p>
                                                <div class="noti-date">5 min ago</div>
                                            </div>
                                        </div>

                                        <div class="noti-item">
                                            <div class="noti-item-icon"><img
                                                    src="{{ asset('schools/images/notification-bing.svg') }}"
                                                    alt="user"></div>
                                            <div class="noti-item-text">
                                                <p>Well done! You successfullyread this important.</p>
                                                <div class="noti-date">5 min ago</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item profile-dropdown dropdown">
                                <a class="nav-link dropdown-toggle" id="profile" data-bs-toggle="dropdown"
                                    aria-expanded="false">
                                    <div class="profile-pic">
                                        <div class="profile-pic-image">
                                            <img src="{{ asset('schools/images/profile.jpg') }}" alt="user">
                                        </div>
                                        <div class="profile-pic-text">
                                            <h3>{{ ucfirst(auth()->user()->name ?? 'Provider') }}</h3>
                                            <p>Provider Admin</p>
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown-menu ">
                                    <a href="{{ route('service_provider.profile') }}" class="dropdown-item px-2">
                                        <i class="las la-user"></i> Profile
                                    </a>
                                    <a href="#" class="dropdown-item px-2" onclick="askLogout()"
                                        style="cursor: pointer">
                                        <i class="las la-sign-out-alt"></i> Logout
                                    </a>
                                </div>
                            </li>
                            <li class="nav-item profile-dropdown dropdown">
                                <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center"
                                    type="button" data-toggle="offcanvas">
                                    <span class="icon-menu"><img
                                            src="{{ asset('schools/images/menu-icon.svg') }}"></span>
                                </button>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
            <div class="body-main-content ">
                @yield('content')
            </div>
        </div>
    </div>
    <div class="modal ee-modal fade" id="logoutpop" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="ee-modal-info">
                        <div class="ee-form-info">
                            <div class="ee-modal-icon">
                                <img src="{{ asset('parents/images/question-circle.svg') }}">
                            </div>
                            <h2>Are You Sure?</h2>
                            <p>Do you really want to logout?</p>
                            <div class="ee-modal-action">
                                <a class="btn-No" class="btn-close" style="cursor: pointer" data-bs-dismiss="modal"
                                    aria-label="Close">No</a>
                                <a class="btn-Yes"href="{{ route('service_provider.logout') }}">Yes</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function askLogout() {
            $("#logoutpop").modal("show");
            return false;
            Swal.fire({
                title: '',
                text: "Are you sure you want to logout?",
                iconHtml: '<img src="{{ asset('images/logo.png') }}" style="background: #141010;height: 80px;border-radius: 36px;">',
                customClass: {
                    icon: 'no-border'
                },
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes'
            }).then((result) => {
                if (result.value) {
                    location.replace("{{ route('service_provider.logout') }}")
                    swal(
                        'Logout',
                        'Yo are logged out successfully.',
                        'success'
                    )
                }
            })
        }
    </script>
    <script>
        $(document).ready(function() {

            if ($("#date")) {
                $("#date").datepicker({
                    dateFormat: 'mm-dd-yy',
                    maxDate: 0, // Maximum selectable date is today
                    changeMonth: true,
                    changeYear: true,
                    yearRange: 'c-100:c', // Allow selection of the past 100 years
                    onSelect: function(dateText) {
                        $(this).val(dateText);
                        if ($("#date").attr("name") == "date") {
                            var dateParts = dateText.split('-');
                            var selectedValue = dateParts[2] + '-' + dateParts[0] + '-' + dateParts[1];

                            var currentUrl = new URL(window.location.href);
                            // Add or update the 'run_id' parameter
                            currentUrl.searchParams.set('date', selectedValue);
                            if (selectedValue == "") {
                                currentUrl.searchParams.delete('date');

                            }
                            // Reload the page with the new URL
                            window.location.href = currentUrl.toString();
                        }

                    }
                });
            }
            if ($("#date_future")) {
                $("#date_future").datepicker({
                    dateFormat: 'mm-dd-yy',
                    // maxDate: 0, // Maximum selectable date_future is today
                    changeMonth: true,
                    changeYear: true,
                    yearRange: "c-100:c+100",
                    onSelect: function(dateText) {
                        $(this).val(dateText);
                        if ($("#date_future").attr("name") == "date") {
                            var dateParts = dateText.split('-');
                            var selectedValue = dateParts[2] + '-' + dateParts[0] + '-' + dateParts[1];

                            var currentUrl = new URL(window.location.href);
                            // Add or update the 'run_id' parameter
                            currentUrl.searchParams.set('date', selectedValue);
                            if (selectedValue == "") {
                                currentUrl.searchParams.delete('date');

                            }
                            // Reload the page with the new URL
                            window.location.href = currentUrl.toString();
                        }

                    }
                });
            }
        })
        // $('.select2').select2();
    </script>

    @if (Session::has('success'))
        <script>
            Swal.fire('Success', '{{ Session('success') }}', 'success');
        </script>
    @endif

    @if (Session::has('error'))
        <script>
            Swal.fire('Error', '{{ Session('error') }}', 'error');
        </script>
    @endif
    @stack('js')
</body>

</html>
