<?php

namespace App\Http\Controllers\Parent;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Program;
use App\Models\School;
use App\Models\User;
use App\Models\ProgramStudentEnrollment;
use App\Models\ParentKidMapping;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MessageController extends Controller
{
    /**
     * Display the messaging dashboard for parents
     */
    public function index(Request $request)
    {
        $filter = $request->get('filter', 'all');
        $category = $request->get('category', null);

        // Get parent's enrolled programs through their kids
        $parentId = auth()->user()->id;
        
        // Get all kids for this parent
        $kidMappings = ParentKidMapping::where('parent_id', $parentId)->get();
        $studentIds = $kidMappings->pluck('student_id');
        
        // Get all programs where parent's kids are enrolled
        $enrollments = ProgramStudentEnrollment::whereIn('student_id', $studentIds)
            ->where('status', 'Active')
            ->with(['program.provider', 'program.school'])
            ->get();
        
        // Get unique providers and schools
        $providers = $enrollments->pluck('program.provider')->unique();
        $schools = $enrollments->pluck('program.school')->unique();
        
        $title = "Messages";
        return view('pages.parents.messages.index', compact('filter', 'title', 'category', 'providers', 'schools', 'enrollments'));
    }

    /**
     * Get recipients based on filter type for parents
     */
    public function getRecipients(Request $request)
    {
        $type = $request->type;
        $id = $request->id;
        $recipients = [];
        $parentId = auth()->user()->id;

        // Get all kids for this parent
        $kidMappings = ParentKidMapping::where('parent_id', $parentId)->get();
        $studentIds = $kidMappings->pluck('student_id');

        switch ($type) {
            case 'provider':
                // Get provider details
                $provider = User::where('user_type', 'service_provider')->findOrFail($id);
                
                $recipients[] = [
                    'id' => 'service_provider_' . $provider->id,
                    'name' => $provider->name . ' (Provider)',
                    'type' => 'service_provider'
                ];
                break;

            case 'school':
                // Get school details
                $school = School::findOrFail($id);
                
                $recipients[] = [
                    'id' => 'school_' . $school->id,
                    'name' => $school->name . ' (School)',
                    'type' => 'school'
                ];
                break;

            case 'all_providers':
                // Get all providers where parent's kids are enrolled
                $enrollments = ProgramStudentEnrollment::whereIn('student_id', $studentIds)
                    ->where('status', 'Active')
                    ->with('program.provider')
                    ->get();
                
                $providers = $enrollments->pluck('program.provider')->unique();
                
                foreach ($providers as $provider) {
                    $recipients[] = [
                        'id' => 'service_provider_' . $provider->id,
                        'name' => $provider->name . ' (Provider)',
                        'type' => 'service_provider'
                    ];
                }
                break;

            case 'all_schools':
                // Get all schools where parent's kids are enrolled
                $enrollments = ProgramStudentEnrollment::whereIn('student_id', $studentIds)
                    ->where('status', 'Active')
                    ->with('program.school')
                    ->get();
                
                $schools = $enrollments->pluck('program.school')->unique();
                
                foreach ($schools as $school) {
                    $recipients[] = [
                        'id' => 'school_' . $school->id,
                        'name' => $school->name . ' (School)',
                        'type' => 'school'
                    ];
                }
                break;
        }

        return response()->json($recipients);
    }

    /**
     * Send a message via Firebase (one-to-one chat)
     */
    public function send(Request $request)
    {
        $request->validate([
            'recipients' => 'required|array',
            'message' => 'required|string',
            'delivery_method' => 'required|in:firebase'
        ]);

        $sender = Auth::user();
        $senderType = 'parent'; // Parent user type
        $senderId = $sender->id;

        try {
            // Process Firebase delivery for one-to-one chat
            foreach ($request->recipients as $recipient) {
                list($recipientType, $recipientId) = explode('_', $recipient);

                // Create a unique chat ID for one-to-one conversation
                $chatId = $this->generateOneToOneChatId($senderType, $senderId, $recipientType, $recipientId);

                // Log the message sending attempt
                Log::info('Parent sending one-to-one message', [
                    'sender' => $senderType . '_' . $senderId,
                    'recipient' => $recipient,
                    'chat_id' => $chatId,
                    'message' => $request->message
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully via Firebase'
            ]);

        } catch (\Exception $e) {
            Log::error('Error sending parent message: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a unique chat ID for one-to-one conversation
     */
    private function generateOneToOneChatId($senderType, $senderId, $recipientType, $recipientId)
    {
        // Create a consistent chat ID regardless of who initiates the conversation
        $participants = [
            $senderType . '_' . $senderId,
            $recipientType . '_' . $recipientId
        ];
        sort($participants);
        return 'one_to_one_' . implode('_', $participants);
    }
}
