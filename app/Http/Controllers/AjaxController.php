<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use App\Models\Lesson;
use App\Models\Notification;
use App\Models\ParentKidMapping;
use App\Models\Student;
use App\Models\Teacher;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class AjaxController extends Controller
{
    //

    public function getTeacherBySchool()
    {
        if (request()->has('school_id')) {



            $school_id = request("school_id");
            $teachers = Teacher::where("school_id", $school_id)->get();

            return response()->json($teachers);
        } else {
            return [];
        }
    }
    public function getStudentByTeacher()
    {
        if (request()->has('teacher_id')) {



            $teacher_id = request("teacher_id");
            $not_in = ParentKidMapping::where("teacher_id", $teacher_id)->groupBy("student_id")->pluck("student_id")->toArray();

            //   dd($not_in);
            $teachers = Student::where("teacher_id", $teacher_id)->whereNotIn("id", $not_in)->get();

            return response()->json($teachers);
        } else {
            return response()->json([]);
        }
    }


    public function getNotifications()
    {
        $guard = Auth::getDefaultDriver();
        $user_type = "user";
        if ($guard == "admin") {
            $user_type = "admin";
        } else if ($guard == "school") {
            $user_type = "school";
        } else if ($guard == "service_provider") {
            $user_type = "service_provider";
        }
        $notifications = Notification::where("to_user_type", $user_type)->where("to_user_id", auth()->user()->id)->get();
        return response()->json($notifications);
    }

    public function getStates()
    {
        if (request()->has("country_id")) {
            $country_id = request("country_id");
            $states = DB::table("states")->where("country_id", $country_id)->get();
            return response()->json($states);
        }
        return [];
    }
    public function sendEmailToInstructor()
    {
        date_default_timezone_set("IST");
        // get all lessons that start in 15 minutes of current date 
        $lessons = Lesson::where("start_time", "<=", date("Y-m-d H:i:s", strtotime("+15 minutes")))->where("lesson_date", date("Y-m-d"))->where("start_time", ">=", date("Y-m-d H:i:s"))->get();
        foreach ($lessons as $key => $value) {
            # code...
            $instructor = $value->schedule->program->instructor;
            if ($instructor) {
                $mailData = [
                    'title' => "Class Reminder",
                    'lines' => [
                        "Dear <strong>{$instructor->name}</strong>,",
                        "This is a reminder that you have a class scheduled in 15 minutes.",
                        "Here are the details:"
                    ],
                    'list' => [
                        "Class Name: <strong>{$value->name}</strong>",
                        "Start Time: <strong>" . date('h:i A', strtotime($value->start_time)) . "</strong>",
                        "Link: <strong><a href=' " . route('instructor.current.class.details', encrypt($value->id)) . "'></a></strong>"
                    ]
                ];
                echo "sent<br>";
                // Mail::send('emails.common', $mailData, function ($message) use ($instructor) {
                //     $message->to($instructor->email)
                //         ->subject("Class Reminder")
                //         ->from(env('MAIL_FROM_ADDRESS'), env("APP_NAME"));
                // });
            }
        }
    }
}
