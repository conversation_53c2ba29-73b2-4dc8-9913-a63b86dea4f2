<?php

namespace App\Http\Controllers\School;

use App\Exports\AttendaneExport;
use App\Exports\TransactionExport;
use App\Http\Controllers\Controller;
use App\Models\ClassBlueprint;
use App\Models\Instructor;
use App\Models\InstructorProgram;
use App\Models\Lesson;
use App\Models\ParentKidMapping;
use App\Models\Program;
use App\Models\ProgramSchedule;
use App\Models\ProgramStudentAttendance;
use App\Models\ProgramStudentEnrollment;
use App\Models\School;
use App\Models\ServiceProvider;
use App\Models\Session;
use App\Models\Student;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class SchoolProgramController extends Controller
{
    protected $auth_id;

    public function __construct() {}
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $programs = Program::when(
            request()->filled('session'),
            fn($query) => $query->where('session', request('session'))
        )
            ->when(
                request()->filled('status'),
                function ($query) {
                    if (request('status') == "completed") {
                        return $query->where('end_date', '<', now());
                    } elseif (request('status') == "ongoing") {
                        return $query->where('start_date', '<=', now())
                            ->where('end_date', '>=', now());
                    }
                }

            )
            ->when(
                request()->filled('provider_id'),
                fn($query) => $query->where('service_provider_id', request('provider_id'))
            )
            ->when(request()->has('days'), function ($query) {
                $query->whereHas('program_schedule', function ($subQuery) {
                    $subQuery->where('days', 'LIKE', '%' . request('days') . '%');
                });
            })
            ->has("program_schedule")
            ->when(request()->has("search"), function ($query) {
                return  $query->where('title',  "LIKE", "%" . request("search") . "%")
                    ->orWhere("description", "LIKE", "%" . request("search") . "%")
                    ->orWhere("fee", "LIKE", "%" . request("search") . "%");
            })
            ->when(request()->has("school_id"), function ($query) {
                return $query->where("school_id", decrypt(request('school_id')));
            })
            ->when(Auth::getDefaultDriver() == "school", function ($query) {
                return $query->where("school_id", auth()->user()->id);
            })
            ->orderBy("id", "desc")
            ->get();
        $title = "Manage Classes";
        $folder = "schools";
        $school = null;
        $schools = [];
        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
            if (request()->has('school_id')) {
                $school = School::find(decrypt($request->school_id));
            }
            $schools = School::all();
        }
        foreach ($programs as $item) {
            $item->total = ProgramStudentEnrollment::where("program_id", $item->id)->count();
        }
        $sessions = Session::when(request()->has("school_id"), function ($query) {
            return $query->where("school_id", decrypt(request('school_id')));
        })
            ->when(Auth::getDefaultDriver() == "school", function ($query) {
                return $query->where("school_id", auth()->user()->id);
            })->get();
        return view("pages.$folder.programs.index", compact('programs', 'title', 'sessions', 'schools', 'school'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $providers = ServiceProvider::when(request()->has("school_id"), function ($query) {
            return $query->where("school_id", decrypt(request('school_id')));
        })
            ->when(Auth::getDefaultDriver() == "school", function ($query) {
                return $query->where("school_id", auth()->user()->id);
            })->get();
        // $providers = ServiceProvider::all();
        $sessions = Session::when(request()->has("school_id"), function ($query) {
            return $query->where("school_id", decrypt(request('school_id')));
        })
            ->when(Auth::getDefaultDriver() == "school", function ($query) {
                return $query->where("school_id", auth()->user()->id);
            })->get();
        $tite = "Create Class";
        $school = null;
        $schools = [];
        $folder = "schools";

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
            if (request()->has('school_id')) {
                $school = School::find(decrypt(request("school_id")));
            } else {
                $schools = School::all();
            }
        }
        $title = "Create Class";

        return view("pages.$folder.programs.create", compact("providers", 'sessions', 'school', 'schools', 'title'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'session' => 'required', // Ensure valid session names
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048', // Image validation
            'service_provider_id' => 'required|exists:service_providers,id',
            'start_date' => 'required', // Start date must be today or later
            'end_date' => 'required', // End date must be after start date
            'fee' => 'required|numeric|min:0',
            'days' => 'required|array|min:1',
            'start_time' => "required",
            'end_time' => "required",
            'grades' => 'nullable|array',
            'location' => 'nullable|string|max:255',
            'min_students' => 'nullable|integer|min:1',
            'max_students' => 'nullable|integer|min:1|gte:min_students',
            'supplies' => 'nullable|string',
        ]);

        $program = new Program();
        $program->title = $request->title;
        $program->session = $request->session;
        $program->description = $request->description;

        $program->service_provider_id = $request->service_provider_id;
        $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
        $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
        $program->fee = $request->fee;

        // Add new fields
        $program->grades = $request->has('grades') ? implode(',', $request->grades) : null;
        $program->location = $request->location;
        $program->min_students = $request->min_students;
        $program->max_students = $request->max_students;
        $program->supplies = $request->supplies;
        $program->is_approved = 1;


        if (Auth::getDefaultDriver() == "admin") {
            $program->school_id = $request->school_id;
        } else {
            $program->school_id = auth()->user()->id;
        }
        $program->status = "Active";
        if ($request->hasFile("image")) {
            $file = $request->file("image");
            $name = "program_" . uniqid() . "." . $file->getClientOriginalExtension();
            $file->move("public/uploads/programs/", $name);
            $program->image = $name;
        }

        if ($program->save()) {

            if ($request->filled('instructor_id')) {
                InstructorProgram::create([
                    'program_id' => $program->id,
                    'instructor_id' => $request->instructor_id,
                    'assigned_at' => now(),
                    'status' => 'Active'
                ]);
            }

            $schedule = new ProgramSchedule();
            $schedule->start_time = $request->start_time;
            $schedule->end_time = $request->end_time;
            $schedule->program_id = $request->program_id;
            $schedule->days = implode(",", $request->days);
            $schedule->year = date("Y");
            $schedule->month = date("m");
            $schedule->program_id = $program->id;
            $schedule->save();
            if ($schedule) {
                $daysArray = explode(',', $schedule->days); // Convert days string to an array
                $currentDate = Carbon::parse($program->start_date);
                $endDate = Carbon::parse($program->end_date);
                $lessonNumber = 1; // Start lesson numbering

                while ($currentDate->lte($endDate)) {
                    if (in_array($currentDate->format('l'), $daysArray)) {
                        DB::table('lessons')->insert([
                            'program_schedule_id' => $schedule->id,
                            'program_id' => $program->id,
                            'title' => 'Lesson ' . $lessonNumber,
                            'description' => 'Lesson ' . $lessonNumber . ' for ' . $program->title,
                            'instructor_id' => $request->instructor_id,
                            'service_provider_id' => $program->service_provider_id,

                            'lesson_date' => $currentDate->toDateString(),
                            'start_time' => $schedule->start_time,
                            'end_time' => $schedule->end_time,
                            'shift' => $this->getShift($schedule->start_time),
                            'lesson_number' => $lessonNumber,
                            'status' => 'Scheduled',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $lessonNumber++; // Increment for next lesson
                    }
                    $currentDate->addDay();
                }
            }
            $user = ServiceProvider::find($program->service_provider_id);
            if ($user) {
                $mailData = [
                    'title' => "Program Assigned on " . env("APP_NAME"),
                    'lines' => [
                        "Dear <strong>{$user->name}</strong>,",
                        "You have been assigned a new program under Everything Enrichment.",
                        "Below are the details of the assigned program:"
                    ],
                    'list' => [
                        "Program Title: <strong>{$program->title}</strong>",
                        "Fee: <strong>$" . number_format($program->fee, 2) . "</strong>"
                    ]
                ];
                // Mail::send('emails.common', $mailData, function ($message) use ($user) {
                //     $message->to($user->email)
                //         ->subject( "Program Assigned on " . env("APP_NAME"))
                //         ->from(env('MAIL_FROM_ADDRESS'), env("APP_NAME"));
                // });
            }


            return successMsg('Class created successfully');
        } else {
            return successMsg('Something went wrong');
        }
    }
    private function getShift($startTime)
    {
        $hour = Carbon::parse($startTime)->hour;
        if ($hour < 12) {
            return 'Morning';
        } elseif ($hour < 17) {
            return 'Afternoon';
        } else {
            return 'Evening';
        }
    }
    /**
     * Display the specified resource.
     */
    public function show($id)
    {

        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $program = Program::find($id);
        $trs = ProgramStudentEnrollment::where("program_id", $id)->when(request()->filled('search'), function ($query) {
            $searchTerm = request('search');

            $studentIds = Student::where('name', 'LIKE', '%' . $searchTerm . '%')->pluck('id')->toArray();

            $query->where(function ($q) use ($studentIds) {
                if (!empty($studentIds)) {
                    $q->orWhereIn('student_id', $studentIds);
                }
            });
        })
            ->when(request()->has('date'), function ($query) {
                return $query->whereDate('created_at', request('date'));
            })
            ->paginate(10);
        if (request()->has('export')) {
            $students = Transaction::whereIn("id",  $trs->pluck("transaction_id")->toArray())->get();
            return Excel::download(new TransactionExport($students), 'transaction_report_' . date("d-m-Y", time()) . '.xlsx');
        }
        $title = "Class Details";


        $startDate = Carbon::parse($program->start_date);
        $endDate =  Carbon::parse($program->end_date);
        $days = collect();
        $currentDate = $startDate;
        $allowed_dates = [];
        $all_allowed_dates = [];
        $daysArray = explode(',', $program->program_schedule->days);
        while ($currentDate <= $endDate) {
            $days->push([
                'day' => $currentDate->format('l'), // Full weekday name (Monday, Tuesday, etc.)
                'date' => $currentDate->format('d M'), // Day number (01, 02, etc.)
                'full_date' => $currentDate->format("Y-m-d"), // Day number (01, 02, etc.)


            ]);
            if (in_array($currentDate->format('l'), $daysArray)) {
                $lesson = Lesson::where('lesson_date', $currentDate)
                    ->where('program_schedule_id', $program->program_schedule->id)
                    ->first();
                if (!$lesson) {
                    $allowed_dates[] = $currentDate->format("Y-m-d");
                }
                $all_allowed_dates[] = $currentDate->format("Y-m-d");
            }
            $currentDate->addDay(); // Move to the next day
        }

        $timeSlots = ['09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '01:00 PM'];
        $lessons = DB::table('lessons')
            ->join('program_schedules', 'lessons.program_schedule_id', '=', 'program_schedules.id')
            ->where('program_schedules.program_id', $id) // Filter by program
            ->select('lessons.*', 'program_schedules.class_name')
            ->orderBy('lesson_date', 'asc')
            ->get();
        $lessons_map = $lessons
            ->map(function ($lesson) use ($program) {
                return [
                    'title' => $lesson->title,
                    'start' => $lesson->lesson_date . 'T' . $lesson->start_time,
                    'end' => $lesson->lesson_date . 'T' . $lesson->end_time,
                    'color' => '#6f51bd', // 👈 this will set the event background
                    'extendedProps' => [
                        'description' => $lesson->description,
                        'lesson_date' => $lesson->lesson_date,
                        'start_time' => $lesson->start_time,
                        'end_time' => $lesson->end_time,
                        'status' => $lesson->status,
                        'updateUrl' =>  route(Auth::getDefaultDriver() . '.program-schedules.update', $lesson->id),
                        'delete_url' => route(Auth::getDefaultDriver() . '.program-schedules.destroy', $lesson->id),

                    ]
                ];
            });

        // dd($lessons);

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";

            $today_schedules = Lesson::whereDate("lesson_date", Carbon::today())
                ->where("program_schedule_id", $program->program_schedule->id)
                ->paginate(10, ['*'], 'today_page');

            $upcoming_schedules = Lesson::where("lesson_date", ">", Carbon::today())
                ->where("program_schedule_id", $program->program_schedule->id)
                ->paginate(10, ['*'], 'upcoming_page');

            return view("pages.admins.programs.show", compact('program', 'title', 'timeSlots', 'lessons_map', 'days', 'lessons', 'trs', 'all_allowed_dates', 'allowed_dates', 'today_schedules', 'upcoming_schedules'));
        }
        $instructors = Instructor::where('service_provider_id', $program->service_provider_id)->get();

        // Get the current instructor assigned to this program
        $programInstructor = InstructorProgram::where('program_id', $program->id)->first();

        return view("pages.schools.programs.show", compact('program', 'title', 'programInstructor', 'timeSlots', 'lessons_map', 'days', 'lessons', 'trs', 'all_allowed_dates', 'allowed_dates'));
        // return view("pages.schools.programs.show", compact("trs", 'trs', 'program',));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $program  = Program::find($id);
        if (strlen($id) > 4) {
            $program  = Program::find(decrypt($id));
        }


        $providers = ServiceProvider::where("school_id", $program->school_id)->get();
        $sessions = Session::where("school_id", $program->school_id)->get();
        $title = "Edit Class";
        $folder = "schools";

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
            if (request()->has('school_id')) {
                $school = School::find(request("school_id"));
            } else {
                $schools = School::all();
            }
        }
        $instructors = Instructor::where('service_provider_id', $program->service_provider_id)->get();

        // Get the current instructor assigned to this program
        $programInstructor = InstructorProgram::where('program_id', $program->id)->first();

        return view("pages.$folder.programs.create", compact("providers", 'title', 'sessions', 'program', 'instructors', 'programInstructor'));
    }
    public function store_schedule(Request $request)
    {
        $request->validate([
            'class_name' => 'required|string|max:255',
            'program_id' => 'required|exists:programs,id',
            'start_time' => 'required|time|after_or_equal:today', // Start time must be today or later
            'end_time' => 'required|time|after:start_time', // End date must be after start date

        ]);
        $program = ProgramSchedule::where("program_id", $request->program_id)->first();
        if (!$program) {
            $program = new ProgramSchedule();
        }
        $program->class_name = $request->class_name;
        $program->start_time = $request->start_time;
        $program->program_id = $request->program_id;

        $program->days = implode(",", $request->days);


        $program->year = date("Y");
        $program->month = date("m");
        $program->program_id = $request->program_id;

        $program->end_time = $request->end_time;


        if ($program->save()) {
            return successMsg('Class  Schedule created successfully');
        } else {
            return successMsg('Something went wrong');
        }
    }
    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Program $program)
    {
        $request->validate([
            'title' => 'sometimes|string|max:255',
            'session' => 'sometimes',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'service_provider_id' => 'sometimes|exists:service_providers,id',
            'days' => 'required|array|min:1',
            'grades' => 'nullable|array',
            'location' => 'nullable|string|max:255',
            'min_students' => 'nullable|integer|min:1',
            'max_students' => 'nullable|integer|min:1|gte:min_students',
            'supplies' => 'nullable|string',
        ]);
        $program->title = $request->title;
        $program->session = $request->session;
        $program->description = $request->description;

        $program->service_provider_id = $request->service_provider_id;
        $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
        $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
        $program->fee = $request->fee;

        // Add new fields
        $program->grades = $request->has('grades') ? implode(',', $request->grades) : null;
        $program->location = $request->location;
        $program->min_students = $request->min_students;
        $program->max_students = $request->max_students;
        $program->supplies = $request->supplies;
        $program->status = $request->status;

        if (Auth::getDefaultDriver() == "admin") {
        } else {
            $program->school_id = auth()->user()->id;
        }
        $program->status = "Active";
        if ($request->hasFile("image")) {
            $file = $request->file("image");
            $name = "program_" . uniqid() . "." . $file->getClientOriginalExtension();
            $file->move("public/uploads/programs/", $name);
            $program->image = $name;
        }

        if ($program->save()) {


            if ($request->filled('instructor_id')) {
                InstructorProgram::updateOrCreate(
                    ['program_id' => $program->id],
                    [
                        'instructor_id' => (int) $request->instructor_id,
                        'assigned_at' => now(),
                        'status' => 'Active'
                    ]
                );
            } else {
                InstructorProgram::where('program_id', $program->id)->delete();
            }


            $schedule =  ProgramSchedule::find($program->program_schedule->id);


            $schedule->start_time = $request->start_time;
            $schedule->end_time = $request->end_time;
            $schedule->program_id = $request->program_id;
            $schedule->days = implode(",", $request->days);
            $schedule->year = date("Y");
            $schedule->month = date("m");
            $schedule->program_id = $program->id;

            $schedule->save();


            return successMsg('Class updated successfully');
        } else {
            return successMsg('Something went wrong');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $program = Program::find($id);
        if ($program) {
            $program->delete();
        }

        return successMsg('Class deleted successfully');
    }


    public function attendance(Request $request, $id)
    {
        $date = Carbon::now();
        if (request()->has('date')) {
            $date = request("date");
        }
        $student_ids = ProgramStudentEnrollment::where("program_id", $id)->pluck("student_id")->toArray();
        $students = Student::whereIn("id", $student_ids)->when(request()->has('search'), function ($query) {
            $keyword = trim(request('search'));
            return $query->where("name", "LIKE", "%$keyword%");
        })->get();
        foreach ($students as $key => $value) {
            $value->attendance = ProgramStudentAttendance::where("student_id", $value->id)->whereDate("date", $date)->first();
        }
        return view("pages.school.programs.attendance", compact('students'));
    }
    public function current_class_details($id)
    {
        $lesson = Lesson::find($id);
        $program = Program::find($lesson->schedule->program_id);
        $student_ids = ProgramStudentEnrollment::where("program_id", $lesson->schedule->program_id)->pluck("student_id")->toArray();
        $students = Student::whereIn("id", $student_ids)->get();
        foreach ($students as $key => $value) {
            $value->attendance = ProgramStudentAttendance::where("student_id", $value->id)->where("lesson_id", $lesson->id)->first();
            $parent = ParentKidMapping::where("student_id", $value->id)->first();
            $value->parent = null;
            if ($parent) {
                $value->parent = User::find($parent->parent_id);
            }
        }
        $folder = "schools";

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
        }

        return view("pages.$folder.programs.current-class", compact('students', 'lesson', 'program'));
    }
    public function program_attendance($id)
    {

        $program = Program::find($id);
        $date = Carbon::now();
        if (request()->has('date')) {
            $date = request("date");
        }
        $student_ids = ProgramStudentEnrollment::where("program_id", $program->id)->pluck("student_id")->toArray();
        $students = Student::whereIn("id", $student_ids)->when(request()->has('search'), function ($query) {
            $keyword = trim(request('search'));
            return $query->where("name", "LIKE", "%$keyword%");
        })->get();
        foreach ($students as $key => $value) {
            $value->attendance = ProgramStudentAttendance::where("student_id", $value->id)->whereDate("date", $date)->first();
        }
        $folder = "schools";

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
        }
        if (request()->has('export')) {
            $students = ProgramStudentAttendance::where("program_id", $id)->whereDate("date", $date)->get();
            return Excel::download(new AttendaneExport($students), 'attendance_report_' . date("d-m-Y", time()) . '.xlsx');
        }
        return view("pages.$folder.programs.attendance", compact('students', 'program'));
    }

    public function markAttendance(Request $request)
    {

        $request->validate([
            'kid_ids' => 'required',
            'lesson_id' => 'required',
            'program_id' => 'required'
        ]);

        foreach ($request->kid_ids as $id) {
            $add = ProgramStudentAttendance::where("student_id", $request->student_id)
                ->where("lesson_id", $request->lesson_id)
                ->whereDate("date", now())
                ->first();
            if (!$add) {
                $add = new ProgramStudentAttendance();
            }

            $add->student_id = $id;
            $add->lesson_id = $request->lesson_id;
            $add->program_id = $request->program_id;
            $add->check_in = $request->time != "" ? $request->time : null;
            $add->date = now();
            $add->save();
        }
        return response()->json(['success' => true, 'message' => count($request->kid_ids) . ' Students']);
    }

    public function markAttendanceCheckout(Request $request)
    {
        if ($request->has("id")) {
            $att = ProgramStudentAttendance::find($request->id);
            if ($att) {
                $match_otp = $request->otp_1 . "" . $request->otp_2 . "" . $request->otp_3 . "" . $request->otp_4;
                $match = ProgramStudentEnrollment::where("otp", $match_otp)->first();
                if (!$match) {
                    return response()->json(['success' => false, 'message' => 'Invalid OTP']);
                }
                $att->check_out = $request->time;
                $att->status = "Present";
                $att->save();
                return response()->json(['success' => true, 'message' => 'checked out Successfully']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Data not found']);
    }

    public function manage_sessions(Request $request)
    {
        $sessions = Session::when(request()->has("school_id"), function ($query) {
            return $query->where("school_id", decrypt(request('school_id')));
        })
            ->when(Auth::getDefaultDriver() == "school", function ($query) {
                return $query->where("school_id", auth()->user()->id);
            })->get();

        $folder = "schools";
        $school = null;
        $schools = [];

        if (Auth::getDefaultDriver() == "admin") {
            $folder = "admins";
            if (request()->has('school_id')) {
                $school = School::find(decrypt($request->school_id));
            }
            $schools = School::all();
        }

        // Enhance sessions with additional data
        foreach ($sessions as $session) {
            // Get programs for this session
            $programs = Program::where('session', $session->name)
                ->when(request()->has("school_id"), function ($query) {
                    return $query->where("school_id", decrypt(request('school_id')));
                })
                ->when(Auth::getDefaultDriver() == "school", function ($query) {
                    return $query->where("school_id", auth()->user()->id);
                })
                ->get();

            // Count classes
            $session->classes_count = $programs->count();

            // Get student enrollments
            $programIds = $programs->pluck('id')->toArray();
            $enrollments = ProgramStudentEnrollment::whereIn('program_id', $programIds)->get();

            // Count unique students
            $session->students = $enrollments->pluck('student_id')->unique()->count();

            // Calculate earnings
            $tids = Transaction::whereIn("program_id", $programIds)->sum("admin_fee");
            $session->earnings = $tids;
        }
        $title = "Manage Sessions";
        return view("pages.$folder.programs.sessions", compact("sessions", 'title', 'school', 'schools'));
    }
    public function store_sessions(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',

            'start_date' => 'required', // Start date must be today or later
            'end_date' => 'required', // End date must be after start date

        ]);

        $program = new Session();
        $program->name = $request->name;
        $program->school_id = auth()->user()->id;
        $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
        $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
        if (Auth::getDefaultDriver() == "admin") {
            $program->school_id = $request->school_id;
        } else {
            $program->school_id = auth()->user()->id;
        }
        if ($program->save()) {
            return successMsg('Session created successfully');
        } else {
            return successMsg('Something went wrong');
        }
    }
    public function update_sessions(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',

            'start_date' => 'required|date|after_or_equal:today', // Start date must be today or later
            'end_date' => 'required|date|after:start_date', // End date must be after start date

        ]);

        $program =  Session::find($id);
        $program->name = $request->name;
        $program->session = $request->session;

        $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
        $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
        if ($program->save()) {
            return successMsg('Session updated successfully');
        } else {
            return successMsg('Something went wrong');
        }
    }
    public function delete_sessions($id)
    {
        $session = Session::find($id);
        if ($session) {
            $session->delete();
            return successMsg('Session deleted successfully');
        }
        return successMsg('Something went wrong');
    }

    public function lesson_store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'program_schedule_id' => 'required',
            'program_id' => 'required|exists:programs,id',
            'lesson_date' => 'required', // Start time must be now or later

        ]);
        $schedule = Lesson::where("program_schedule_id", $request->program_schedule_id)->where("lesson_date")->first();
        if (!$schedule) {
            $sc = ProgramSchedule::find($request->program_schedule_id);
            DB::table('lessons')->insert([
                'program_schedule_id' => $request->program_schedule_id,
                'program_id' => $request->program_id,
                'title' => $request->title,
                'description' => $request->description,
                'instructor_id' => 'TBD',
                'service_provider_id' => 'TBD',
                'lesson_date' => $request->date,
                'start_time' => $sc->start_time,
                'end_time' => $sc->end_time,
                'shift' => $this->getShift($sc->start_time),
                'status' => 'Scheduled',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }


        return successMsg('Lesson   created successfully');
    }
    public function getAttendance($id)
    {
        $program = Program::findOrFail($id);
        $date = $request->date ?? date('Y-m-d');

        // Get lesson for the selected date
        $lesson = Lesson::whereDate('lesson_date', $date)
            ->whereHas('schedule', function ($q) use ($program) {
                $q->where('program_id', $program->id);
            })
            ->first();

        // Get attendance records for this lesson
        $attendanceRecords = $lesson ?
            ProgramStudentAttendance::where('lesson_id', $lesson->id)->get() :
            collect([]);

        // Get all enrolled students
        $enrolledStudents = ProgramStudentEnrollment::where('program_id', $program->id)
            ->where('status', 'Active')
            ->with('student')
            ->get();

        // Prepare data for the view
        $students = [];
        foreach ($enrolledStudents as $enrollment) {
            $attendance = $attendanceRecords->where('student_id', $enrollment->student_id)->first();
            $students[] = [
                'id' => $enrollment->student_id,
                'name' => $enrollment->student->name,
                'check_in' => $attendance->check_in ?? null,
                'check_out' => $attendance->check_out ?? null,
                'attendance' => $attendance
            ];
        }

        return view('pages.admins.programs.attendance-table', compact('students', 'date'))->render();
    }
    /**
     * Display classes for a specific session
     */
    public function sessionClasses($id)
    {
        $session = Session::findOrFail($id);

        // Get school information
        $school = School::find($session->school_id);

        // Get programs for this session
        $programs = Program::where('session', $session->name)
            ->where('school_id', $session->school_id)
            ->get();

        // Enhance programs with enrollment data
        foreach ($programs as $program) {
            // Get enrollments
            $enrollments = ProgramStudentEnrollment::where('program_id', $program->id)->get();

            // Count students
            $program->students_count = $enrollments->count();

            // Calculate earnings
            $program->earnings = Transaction::where("program_id", $program->id)->sum("admin_fee");

            // Get student details
            $studentIds = $enrollments->pluck('student_id')->toArray();
            $program->students = Student::whereIn('id', $studentIds)->get();

            // Get grade distribution
            $program->grades = $program->students->groupBy('grade')->map->count();
        }

        $folder = Auth::getDefaultDriver() == "admin" ? "admins" : "schools";

        return view("pages.$folder.programs.session_classes", compact('session', 'programs', 'school'));
    }
    public function approve_changes($id)
    {
        $program = Program::find($id);
        if ($program) {
            $program->status = "Active";
            $program->save();
            // create lessons 
            $program->createLessons();
            return successMsg('Program approved successfully');
        }
        return errorMsg('Something went wrong');
    }
}
