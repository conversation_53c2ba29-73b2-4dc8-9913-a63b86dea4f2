<?php

namespace App\Http\Controllers\Provider;

use App\Exports\AttendaneExport;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\ClassBlueprint;
use App\Models\ClassDraft;
use App\Models\Instructor;
use App\Models\InstructorProgram;
use App\Models\Lesson;
use App\Models\ParentKidMapping;
use App\Models\Program;
use App\Models\ProgramBlueprint;
use App\Models\ProgramSchedule;
use App\Models\ProgramDraft;
use App\Models\ProgramStudentAttendance;
use App\Models\ProgramStudentEnrollment;
use App\Models\ProviderRequirement;
use App\Models\School;
use App\Models\SchoolProvider;
use App\Models\SchoolRequest;
use App\Models\ServiceProvider;
use App\Models\Session;
use App\Models\Student;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Excel;
use Maatwebsite\Excel\Facades\Excel as FacadesExcel;

class ProviderClassController extends Controller
{
    public function class_details($id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $user = ServiceProvider::find(auth()->user()->id);
        // $enroll = ProgramStudentEnrollment::find($id);
        // if (!$enroll) {
        //     return back()->with('error', 'Something went wrong');
        // }
        $program = Program::find($id);
        if (!$program) {
            return back()->with('error', 'Something went wrong');
        }

        $programsschedules = ProgramSchedule::where("program_id", $id)
            ->pluck("id")->toArray();

        $today_schedules = Lesson::whereDate("lesson_date", Carbon::today())
            ->whereIn("program_schedule_id", $programsschedules)
            ->paginate(10, ['*'], 'today_page');

        $upcoming_schedules = Lesson::where("lesson_date", ">", Carbon::today())
            ->whereIn("program_schedule_id", $programsschedules)
            ->paginate(10, ['*'], 'upcoming_page');

        $archive_schedules = Lesson::where("lesson_date", "<", Carbon::today())
            ->whereIn("program_schedule_id", $programsschedules)
            ->orderBy("id", "desc")
            ->paginate(10, ['*'], 'archive_page');
        $instructors = Instructor::where('service_provider_id', auth()->user()->id)->get();

        return view("pages.providers.class-details", compact('program', 'today_schedules', 'upcoming_schedules', 'archive_schedules', 'instructors'));
    }

    public function current_class_details($id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $lesson = Lesson::find($id);
        $program = Program::find($lesson->schedule->program_id);
        $student_ids = ProgramStudentEnrollment::where("program_id", $lesson->schedule->program_id)->pluck("student_id")->toArray();
        $students = Student::whereIn("id", $student_ids)->when(request()->filled('search'), function ($query) {
            $searchTerm = request('search');
            return $query->where('name', 'LIKE', '%' . $searchTerm . '%');
        })->get();
        foreach ($students as $key => $value) {
            $value->attendance = ProgramStudentAttendance::where("student_id", $value->id)->where("lesson_id", $lesson->id)->first();
            $parent = ParentKidMapping::where("student_id", $value->id)->first();
            $value->parent = null;
            if ($parent) {
                $value->parent = User::find($parent->parent_id);
            }
        }
        return view("pages.providers.current-class", compact('lesson', 'students', 'program'));
    }
    public function program_attendance(Request $request, $id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $lesson = Lesson::find($id);
        $program = Program::find($lesson->schedule->program_id);
        $date = Carbon::now();
        if (request()->has('date')) {
            $date = request("date");
        }
        $student_ids = ProgramStudentEnrollment::where("program_id", $lesson->schedule->program_id)->pluck("student_id")->toArray();
        $students = Student::whereIn("id", $student_ids)->when(request()->has('search'), function ($query) {
            $keyword = trim(request('search'));
            return $query->where("name", "LIKE", "%$keyword%");
        })->get();
        foreach ($students as $key => $value) {
            $value->attendance = ProgramStudentAttendance::where("student_id", $value->id)->whereDate("date", $date)->first();
        }
        if ($request->has('export')) {
            $recent_enrollments = ProgramStudentAttendance::whereIn("student_id", $students->pluck("id")->toArray())->whereDate("date", $date)->get();
            return FacadesExcel::download(new AttendaneExport($recent_enrollments),  'attendance_report_' . date("d-m-Y", time()) . '.xlsx');
        }
        return view("pages.providers.program-attendance", compact('lesson', 'students', 'program'));
    }

    public function assigned_programs()
    {
        $provider_id = auth()->user()->id;

        $programs = Program::when(request()->has('search'), fn($query) => $query->where('title', 'LIKE', '%' . request('search') . '%'))
            ->where("service_provider_id", $provider_id)
            ->where("status", "Active")
            ->get();
        return view("pages.providers.assigned-programs", compact('programs'));
    }

    public function assigned_programs_details($id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $user = ServiceProvider::find(auth()->user()->id);
        // $enroll = ProgramStudentEnrollment::find($id);
        // if (!$enroll) {
        //     return back()->with('error', 'Something went wrong');
        // }
        $program = Program::find($id);
        if (!$program) {
            return back()->with('error', 'Something went wrong');
        }
        $timeSlots = ['09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '01:00 PM'];
        $lessons = DB::table('lessons')
            ->join('program_schedules', 'lessons.program_schedule_id', '=', 'program_schedules.id')
            ->where('program_schedules.program_id', $program->id) // Filter by program
            ->select('lessons.*', 'program_schedules.class_name')
            ->orderBy('lesson_date', 'asc')
            ->get();
        $startDate = Carbon::parse($program->start_date);
        $endDate =  Carbon::parse($program->end_date);
        $days = collect();
        $currentDate = $startDate;
        $allowed_dates = [];
        $all_allowed_dates = [];
        $daysArray = explode(',', $program->program_schedule->days);
        while ($currentDate <= $endDate) {
            $days->push([
                'day' => $currentDate->format('l'), // Full weekday name (Monday, Tuesday, etc.)
                'date' => $currentDate->format('d M'), // Day number (01, 02, etc.)
                'full_date' => $currentDate->format("Y-m-d"), // Day number (01, 02, etc.)


            ]);
            if (in_array($currentDate->format('l'), $daysArray)) {
                $lesson = Lesson::where('lesson_date', $currentDate)
                    ->where('program_schedule_id', $program->program_schedule->id)
                    ->first();
                if (!$lesson) {
                    $allowed_dates[] = $currentDate->format("Y-m-d");
                }
                $all_allowed_dates[] = $currentDate->format("Y-m-d");
            }
            $currentDate->addDay(); // Move to the next day
        }

        $timeSlots = ['09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '01:00 PM'];
        $lessons = DB::table('lessons')
            ->join('program_schedules', 'lessons.program_schedule_id', '=', 'program_schedules.id')
            ->where('program_schedules.program_id', $id) // Filter by program
            ->select('lessons.*', 'program_schedules.class_name')
            ->orderBy('lesson_date', 'asc')
            ->get();
        $lessons_map = $lessons
            ->map(function ($lesson) use ($program) {
                return [
                    'title' => $lesson->title,
                    'start' => $lesson->lesson_date . 'T' . $lesson->start_time,
                    'end' => $lesson->lesson_date . 'T' . $lesson->end_time,
                    'color' => '#6f51bd', // 👈 this will set the event background
                    'extendedProps' => [
                        'description' => $lesson->description,
                        'lesson_date' => $lesson->lesson_date,
                        'start_time' => $lesson->start_time,
                        'end_time' => $lesson->end_time,
                        'status' => $lesson->status,
                        'updateUrl' => route('service_provider.lesson.update', $lesson->id),
                        'delete_url' => route('service_provider.lesson.destory', $lesson->id)
                    ],
                ]; // <-- Should be ']' but ended with a semicolon
            }); // <-

        $school = School::find($user->school_id);
        return view("pages.providers.assigned-program-details", compact('user', 'allowed_dates', 'all_allowed_dates', 'lessons_map', 'days', 'program', 'timeSlots', 'lessons', 'school'));
    }
    public function schedule()
    {

        $programs = Program::where("service_provider_id", auth()->user()->id)
            ->where("status", "Active")
            ->pluck("id")->toArray();
        $timeSlots = ['09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '01:00 PM'];
        $lessons = DB::table('lessons')
            ->join('program_schedules', 'lessons.program_schedule_id', '=', 'program_schedules.id')
            ->whereIn('program_schedules.program_id', $programs) // Filter by program
            ->select('lessons.*', 'program_schedules.class_name')
            ->orderBy('lesson_date', 'asc')
            ->get();
        // Get the first and last day of the current month
        $startDate = Carbon::now()->startOfMonth(); // First day of the current month
        $endDate = Carbon::now()->endOfMonth(); // Last day of the current month
        $lessons_map = $lessons
            ->map(function ($lesson) {
                return [
                    'title' => $lesson->title,
                    'start' => $lesson->lesson_date . 'T' . $lesson->start_time,
                    'end' => $lesson->lesson_date . 'T' . $lesson->end_time,
                    'color' => '#6f51bd', // 👈 this will set the event background
                    'extendedProps' => [
                        'description' => $lesson->description,
                        'lesson_date' => $lesson->lesson_date,

                        'updateUrl' => route('admin.program-schedules.update', $lesson->id),
                    ]
                ];
            });
        // Generate an array with all days of the current month
        $days = collect();
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $days->push([
                'day' => $currentDate->format('l'), // Full weekday name (Monday, Tuesday, etc.)
                'date' => $currentDate->format('d M'), // Day number (01, 02, etc.)
                'full_date' => $currentDate->format("Y-m-d"), // Day number (01, 02, etc.)


            ]);
            $currentDate->addDay(); // Move to the next day
        }
        return view("pages.providers.schedule", compact('days',  'timeSlots', 'lessons', 'lessons_map'));
    }

    public function classess()
    {
        $provider_id = auth()->user()->id;

        // Get blueprints
        $blueprints = ClassBlueprint::where('provider_id', $provider_id)->get();

        // Get instructors
        $instructors = Instructor::where('service_provider_id', $provider_id)
            ->get();

        // Get schools
        $schools = School::all();

        // Get draft classes
        $drafts = ClassDraft::where('provider_id', $provider_id)
            ->when(request()->has('session'), function ($query) {
                return $query->where('session', request('session'));
            })
            ->get();

        // Get class requests from schools
        $requests = Program::where('service_provider_id', $provider_id)
            ->where('status', 'Inative')
            ->when(request()->has('session'), function ($query) {
                return $query->where('session', request('session'));
            })
            ->get();

        // Get published classes with filtering
        $published = Program::where('service_provider_id', $provider_id)
            ->where('status', 'Active')
            ->when(request()->has('search'), function ($query) {
                return $query->where('title', 'LIKE', '%' . request('search') . '%');
            })
            ->when(request()->has('session'), function ($query) {
                return $query->where('session', request('session'));
            })
            ->when(request()->has('status'), function ($query) {
                $status = request('status');
                if ($status === 'ongoing') {
                    return $query->where('start_date', '<=', now())
                        ->where('end_date', '>=', now());
                } elseif ($status === 'upcoming') {
                    return $query->where('start_date', '>', now());
                } elseif ($status === 'completed') {
                    return $query->where('end_date', '<', now());
                } elseif ($status === 'enrolling') {
                    return $query->where('enrollment_start', '<=', now())
                        ->where('enrollment_end', '>=', now());
                }
                return $query;
            })

            ->orderBy('created_at', 'desc')
            ->paginate(10);
        // Get published classes with filtering
        $unpublished = Program::where('service_provider_id', $provider_id)
            ->where('status', 'Inactive')
            ->when(request()->has('search'), function ($query) {
                return $query->where('title', 'LIKE', '%' . request('search') . '%');
            })
            ->when(request()->has('session'), function ($query) {
                return $query->where('session', request('session'));
            })
            ->when(request()->has('status'), function ($query) {
                $status = request('status');
                if ($status === 'ongoing') {
                    return $query->where('start_date', '<=', now())
                        ->where('end_date', '>=', now());
                } elseif ($status === 'upcoming') {
                    return $query->where('start_date', '>', now());
                } elseif ($status === 'completed') {
                    return $query->where('end_date', '<', now());
                } elseif ($status === 'enrolling') {
                    return $query->where('enrollment_start', '<=', now())
                        ->where('enrollment_end', '>=', now());
                }
                return $query;
            })

            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Add attendance information for published classes
        foreach ($published as $class) {
            $totalEnrolled = ProgramStudentEnrollment::where('program_id', $class->id)->count();
            $present = ProgramStudentAttendance::whereDate('date', now())
                ->where('program_id', $class->id)
                ->count();

            $class->total_enrolled = $totalEnrolled;
            $class->present_today = $present;

            $hasLessonToday = Lesson::where('program_schedule_id', $class->program_schedule->id)
                ->whereDate('lesson_date', now())
                ->exists();

            $class->absent_today = $hasLessonToday ? ($totalEnrolled - $present) : 0;

            // Calculate class status
            if ($class->end_date < now()) {
                $class->status_label = 'completed';
            } elseif ($class->start_date > now()) {
                $class->status_label = 'upcoming';
            } elseif ($class->enrollment_start <= now() && $class->enrollment_end >= now()) {
                $class->status_label = 'enrolling';
            } else {
                $class->status_label = 'ongoing';
            }
        }
        $programs = Program::where("service_provider_id", auth()->user()->id)->get();
        $sessions = Session::where("school_id", auth()->user()->school_id)->get();
        $title = "Classes";
        return view('pages.providers.new_classess', compact(
            'blueprints',
            'drafts',
            'requests',
            'published',
            'schools',
            'instructors',
            'programs',
            'sessions',
            'unpublished',
            'title'
        ));
    }
    public function markAttendance(Request $request)
    {

        $request->validate([
            'kid_ids' => 'required',
            'lesson_id' => 'required',
            'program_id' => 'required'
        ]);

        foreach ($request->kid_ids as $id) {
            $add = ProgramStudentAttendance::where("student_id", $request->student_id)
                ->where("lesson_id", $request->lesson_id)
                ->whereDate("date", now())
                ->first();
            if (!$add) {
                $add = new ProgramStudentAttendance();
            }
            $lesson = Lesson::find($request->lesson_id);
            $add->student_id = $id;
            $add->lesson_id = $request->lesson_id;
            $add->program_id = $request->program_id;
            $add->check_in = $request->time != "" ? $request->time : null;
            $add->date = $lesson->lesson_date;
            $add->save();
        }
        return response()->json(['success' => true, 'message' => count($request->kid_ids) . ' Students']);
    }

    public function markAttendanceCheckout(Request $request)
    {
        if ($request->has("id")) {
            $att = ProgramStudentAttendance::find($request->id);
            if ($att) {
                $match_otp = $request->otp_1 . "" . $request->otp_2 . "" . $request->otp_3 . "" . $request->otp_4;
                $match = ProgramStudentEnrollment::where("otp", $match_otp)->first();
                if (!$match) {
                    return response()->json(['success' => false, 'message' => 'Invalid OTP']);
                }
                $att->check_out = $request->time;
                $att->status = "Present";
                $att->save();
                return response()->json(['success' => true, 'message' => 'Check out Successfully']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Data not found']);
    }

    public function studentDetails(Request $request, $program_id, $student_id)
    {


        if (request()->has("date")) {
            $date = request("date");
        }
        $student = Student::find($student_id);
        $program = Program::find($program_id);

        $attendance = ProgramStudentAttendance::where("student_id", $student_id)->where("program_id", $program_id)
            ->when(request()->has('date'), function ($query) {
                return $query->whereDate("date", request('date'));
            })->get();
        if ($request->has('export')) {

            return FacadesExcel::download(new AttendaneExport($attendance),  'attendance_report_' . date("d-m-Y", time()) . '.xlsx');
        }

        return view("pages.providers.student-details", compact("student", 'attendance', 'program'));
    }
    private function getShift($startTime)
    {
        $hour = Carbon::parse($startTime)->hour;
        if ($hour < 12) {
            return 'Morning';
        } elseif ($hour < 17) {
            return 'Afternoon';
        } else {
            return 'Evening';
        }
    }
    public function lesson_store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'program_schedule_id' => 'required',
            'program_id' => 'required|exists:programs,id',
            'lesson_date' => 'required', // Start time must be now or later

        ]);
        $schedule = Lesson::where("program_schedule_id", $request->program_schedule_id)->where("lesson_date")->first();
        $sc = ProgramSchedule::find($request->program_schedule_id);
        if (!$schedule) {

            DB::table('lessons')->insert([
                'program_schedule_id' => $request->program_schedule_id,
                'program_id' => $request->program_id,
                'title' => $request->title,
                'description' => $request->description,
                'instructor' => 'TBD',
                'lesson_date' => $request->lesson_date,
                'start_time' => $sc->start_time,
                'end_time' => $sc->end_time,
                'shift' => $this->getShift($sc->start_time),
                'status' => 'Scheduled',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $schedule->title = $request->title;
            $schedule->description = $request->description;
            $schedule->start_time = $sc->start_time;
            $schedule->end_time = $sc->end_time;
            $schedule->save();
        }


        return successMsg('Lesson created successfully');
    }
    public function lesson_update(Request $request, $program)
    {
        $lesson = Lesson::find($program);
        if ($lesson) {
            $lesson->title = $request->title;
            $lesson->lesson_date = $request->lesson_date;
            $lesson->start_time = $request->start_time;
            $lesson->end_time = $request->end_time;
            $lesson->status = $request->status;

            $lesson->description = $request->description;
            $lesson->save();
            if ($request->status == 'Cancelled') {
                try {
                    $recipients = [];
                    $enrollments = ProgramStudentEnrollment::where("program_id", $lesson->program_id)->get();
                    foreach ($enrollments as $key => $value) {
                        # code...
                        $recipients[] = [
                            'email' => $value->parent->email,
                            'parentName' =>  $value->parent->name,
                            'childName' =>  $value->student->name,
                            'programName' => $lesson->name,
                            'date' => date('M d, Y', strtotime($lesson->lesson_date)),
                            'time' => date('h:i A', strtotime($lesson->start_time)),
                        ];
                    }

                    foreach ($recipients as $person) {
                        // Mail::to($person['email'])->send(new ProgramCancelledMail(
                        //     $person['parentName'],
                        //     $person['childName'],
                        //     $person['programName'],
                        //     $person['date'],
                        //     $person['time']
                        // ));
                    }
                } catch (\Throwable $th) {
                    info("Lesson canceled mail not sent");
                }
            }
        }
        return successMsg('Lesson  updated successfully');
    }
    public function lesson_destory($program)
    {
        $lesson = Lesson::find($program);
        if ($lesson) {
            $lesson->delete();
        }

        return successMsg('Lesson deleted successfully');
    }

    /**
     * Create a new class draft
     */
    public function createDraft(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|exists:schools,id',
            'title' => 'required|string|max:255',
            'session' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'day_of_week' => 'required|string',
            'start_time' => 'required',
            'end_time' => 'required',
            'location' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'enrollment_start' => 'required|date',
            'enrollment_end' => 'required|date|after_or_equal:enrollment_start',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $draft = new ClassDraft();
            $draft->provider_id = Auth::id();
            $draft->school_id = $request->school_id;
            $draft->title = $request->title;
            $draft->session = $request->session;
            $draft->description = $request->description;
            $draft->start_date = $request->start_date;
            $draft->end_date = $request->end_date;
            $draft->no_class_dates = $request->no_class_dates;
            $draft->min_students = $request->min_students;
            $draft->max_students = $request->max_students;
            $draft->age_range = $request->age_range;
            $draft->cost = $request->cost;
            $draft->supply_cost = $request->supply_cost ?? 0;
            $draft->day_of_week = $request->day_of_week;
            $draft->start_time = $request->start_time;
            $draft->end_time = $request->end_time;
            $draft->location = $request->location;
            $draft->instructor_id = $request->instructor_id;
            $draft->enrollment_start = $request->enrollment_start;
            $draft->enrollment_end = $request->enrollment_end;
            $draft->registration_questions = $request->registration_questions;
            $draft->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $draft->is_draft = 1;
            $draft->save();

            return response()->json([
                'success' => true,
                'message' => 'Draft created successfully',
                'redirect' => true,
                'route' => route('service_provider.edit_draft', $draft->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Edit a class draft
     */
    public function editDraft($id)
    {
        $draft = ClassDraft::findOrFail($id);

        // Check if draft belongs to the provider
        if ($draft->provider_id != Auth::id()) {
            return redirect()->route('service_provider.classes')
                ->with('error', 'Unauthorized access');
        }

        return view('pages.providers.edit-draft', compact('draft'));
    }

    /**
     * Update a class draft
     */
    public function updateDraft(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|exists:schools,id',
            'title' => 'required|string|max:255',
            'session' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'day_of_week' => 'required|string',
            'start_time' => 'required',
            'end_time' => 'required',
            'location' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'enrollment_start' => 'required|date',
            'enrollment_end' => 'required|date|after_or_equal:enrollment_start',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $draft = ClassDraft::findOrFail($id);

            // Check if draft belongs to the provider
            if ($draft->provider_id != Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ]);
            }

            $draft->school_id = $request->school_id;
            $draft->title = $request->title;
            $draft->session = $request->session;
            $draft->description = $request->description;
            $draft->start_date = $request->start_date;
            $draft->end_date = $request->end_date;
            $draft->no_class_dates = $request->no_class_dates;
            $draft->min_students = $request->min_students;
            $draft->max_students = $request->max_students;
            $draft->age_range = $request->age_range;
            $draft->cost = $request->cost;
            $draft->supply_cost = $request->supply_cost ?? 0;
            $draft->day_of_week = $request->day_of_week;
            $draft->start_time = $request->start_time;
            $draft->end_time = $request->end_time;
            $draft->location = $request->location;
            $draft->instructor_id = $request->instructor_id;
            $draft->enrollment_start = $request->enrollment_start;
            $draft->enrollment_end = $request->enrollment_end;
            $draft->registration_questions = $request->registration_questions;
            $draft->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $draft->save();

            return response()->json([
                'success' => true,
                'message' => 'Draft updated successfully',
                'redirect' => true,
                'route' => route('service_provider.classes')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Publish a draft as a class
     */
    public function publishDraft(Request $request, $id)
    {
        try {
            $draft = ClassDraft::findOrFail($id);

            // Check if draft belongs to the provider
            if ($draft->provider_id != Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ]);
            }

            // Validate required fields
            $validator = Validator::make($draft->toArray(), [
                'school_id' => 'required|exists:schools,id',
                'title' => 'required|string|max:255',
                'session' => 'required|string',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'min_students' => 'required|integer|min:1',
                'max_students' => 'required|integer|gte:min_students',
                'description' => 'required|string',
                'age_range' => 'required|string',
                'cost' => 'required|numeric|min:0',
                'day_of_week' => 'required|string',
                'start_time' => 'required',
                'end_time' => 'required',
                'location' => 'required|string',
                'instructor_id' => 'required|exists:users,id',
                'enrollment_start' => 'required|date',
                'enrollment_end' => 'required|date|after_or_equal:enrollment_start',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Draft is incomplete. Please fill in all required fields.',
                    'errors' => $validator->errors()
                ]);
            }

            // Create new program from draft
            $program = new Program();
            $program->provider_id = Auth::id();
            $program->school_id = $draft->school_id;
            $program->instructor_id = $draft->instructor_id;
            $program->title = $draft->title;
            $program->session = $draft->session;
            $program->description = $draft->description;
            $program->start_date = $draft->start_date;
            $program->end_date = $draft->end_date;
            $program->no_class_dates = $draft->no_class_dates;
            $program->min_students = $draft->min_students;
            $program->max_students = $draft->max_students;
            $program->age_range = $draft->age_range;
            $program->cost = $draft->cost;
            $program->supply_cost = $draft->supply_cost ?? 0;
            $program->day_of_week = $draft->day_of_week;
            $program->start_time = $draft->start_time;
            $program->end_time = $draft->end_time;
            $program->location = $draft->location;
            $program->enrollment_start = $draft->enrollment_start;
            $program->enrollment_end = $draft->enrollment_end;
            $program->registration_questions = $draft->registration_questions;
            $program->enable_checkin = $draft->enable_checkin;
            $program->status = 'upcoming';
            $program->save();

            // Delete the draft
            $draft->delete();

            return response()->json([
                'success' => true,
                'message' => 'Draft published successfully',
                'redirect' => true,
                'route' => route('service_provider.class_details', $program->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show request details
     */
    public function requestDetails($id)
    {
        $request = SchoolRequest::with('school')->findOrFail($id);

        // Check if request is for the provider's school
        if (!$request->school || $request->school->provider_id != Auth::id()) {
            return redirect()->route('service_provider.classes')
                ->with('error', 'Unauthorized access');
        }

        return view('pages.providers.request-details', compact('request'));
    }

    /**
     * Respond to a school request
     */
    public function respondToRequest(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'response' => 'required|string',
            'status' => 'required|in:Accepted,Declined,In Progress',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $schoolRequest = SchoolRequest::with('school')->findOrFail($id);

            // Check if request is for the provider's school
            if (!$schoolRequest->school || $schoolRequest->school->provider_id != Auth::id()) {
                return redirect()->route('service_provider.classes')
                    ->with('error', 'Unauthorized access');
            }

            // Update request
            $schoolRequest->provider_response = $request->response;
            $schoolRequest->status = $request->status;
            $schoolRequest->responded_at = now();
            $schoolRequest->save();

            // If accepted and it's a class request, create a new class
            if ($request->status == 'Accepted' && $schoolRequest->type == 'Class Request') {
                // Create a draft for the provider to complete
                $draft = new ClassDraft();
                $draft->provider_id = Auth::id();
                $draft->school_id = $schoolRequest->school_id;
                $draft->title = $schoolRequest->title ?? 'New Class from Request';
                $draft->description = $schoolRequest->details;
                // Set other fields with default values or from request if available
                $draft->save();

                return redirect()->route('service_provider.edit_draft', $draft->id)
                    ->with('success', 'Request accepted. Please complete the class details.');
            }

            return redirect()->route('service_provider.classes')
                ->with('success', 'Response submitted successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Create a new blueprint
     */
    public function storeBlueprint(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'supply_cost' => 'nullable|numeric|min:0',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'day_of_week' => 'required|string',
            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
            'location' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $blueprint = new ClassBlueprint();
            $blueprint->provider_id = Auth::id();
            $blueprint->title = $request->title;
            $blueprint->description = $request->description;
            $blueprint->age_range = $request->age_range;
            $blueprint->cost = $request->cost;
            $blueprint->supply_cost = $request->supply_cost ?? 0;
            $blueprint->min_students = $request->min_students;
            $blueprint->max_students = $request->max_students;
            $blueprint->day_of_week = $request->day_of_week;
            $blueprint->start_time = $request->start_time;
            $blueprint->end_time = $request->end_time;
            $blueprint->location = $request->location;
            $blueprint->registration_questions = $request->registration_questions;
            $blueprint->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $blueprint->usage_count = 0;
            $blueprint->save();

            return response()->json([
                'success' => true,
                'message' => 'Blueprint created successfully',
                'redirect' => true,
                'route' => route('service_provider.classes')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a blueprint
     */
    public function deleteBlueprint($id)
    {
        try {
            $blueprint = ClassBlueprint::findOrFail($id);

            // Check if blueprint belongs to the provider
            if ($blueprint->provider_id != Auth::id()) {
                return redirect()->back()->with('error', 'Unauthorized access');
            }

            $blueprint->delete();

            return redirect()->route('service_provider.classes')
                ->with('success', 'Blueprint deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a class
     */
    public function cancelClass(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $class = Program::with(['enrollments.student.parent'])->findOrFail($id);

            // Check if class belongs to the provider
            if ($class->provider_id != Auth::id()) {
                return redirect()->route('service_provider.classes')
                    ->with('error', 'Unauthorized access');
            }

            // Update class status
            $class->status = 'cancelled';
            $class->cancellation_reason = $request->cancellation_reason;
            $class->cancelled_at = now();
            $class->cancelled_by = Auth::id();
            $class->save();

            // Notify enrolled students' parents
            foreach ($class->enrollments as $enrollment) {
                if ($enrollment->student && $enrollment->student->parent) {
                    // Send email notification
                    $mailData = [
                        'parentName' => $enrollment->student->parent->name,
                        'childName' => $enrollment->student->name,
                        'programName' => $class->title,
                        'date' => date('m/d/Y', strtotime($class->start_date)),
                        'time' => date('h:i A', strtotime($class->start_time)),
                        'reason' => $request->cancellation_reason
                    ];

                    Mail::send('mails.program_cancelled', $mailData, function ($message) use ($enrollment) {
                        $message->to($enrollment->student->parent->email)
                            ->subject('Class Cancellation Notice');
                    });
                }
            }

            return redirect()->route('service_provider.classes')
                ->with('success', 'Class cancelled successfully. Enrolled students have been notified.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }
    public function attendance(Request $request)
    {
        $provider_id = auth()->user()->id;

        $viewDate = $request->input('date') ? Carbon::parse($request->input('date')) : Carbon::today();

        $previousDayDate = $viewDate->copy()->subDay();
        $nextDayDate = $viewDate->copy()->addDay();

        // Get schools, classes and activities for filters
        $schools = School::whereHas('programs', function ($q) use ($provider_id) {
            $q->where('service_provider_id', $provider_id);
        });
        $all_schools = $schools->get();
        $schools = $schools->when(request()->has('school'), function ($query) {
            return $query->where('id', request('school'));
        })
            ->get();

        $classes = Program::where('service_provider_id', $provider_id)
            ->with(['lessons' => function ($q) use ($viewDate) {
                $q->whereDate('lesson_date', $viewDate);
            }]);
        $all_classes = $classes->get();

        $classes =         $classes->when(request()->has('school'), function ($query) {
            return $query->where('school_id', request('school'));
        })->when(request()->has('class'), function ($query) {
            return $query->where('id', request('class'));
        })
            ->withCount([
                'enrollments as enrolled_count',
                'attendances as checked_in_count' => function ($q) use ($viewDate) {
                    $q->whereDate('date', $viewDate)->whereNotNull('check_in');
                },
                'attendances as checked_out_count' => function ($q) use ($viewDate) {
                    $q->whereDate('date', $viewDate)->whereNotNull('check_out');
                }
            ])
            ->get();

        $activities = Program::where('service_provider_id', $provider_id)
            ->select('title')
            ->distinct()
            ->get();

        // Get summary counts
        $totalEnrolled = $classes->sum('enrolled_count');
        $checkedIn = $classes->sum('checked_in_count');
        $checkedOut = $classes->sum('checked_out_count');
        $title = "Attendance";
        return view('pages.providers.attendance', compact(
            'viewDate',
            'previousDayDate',
            'nextDayDate',
            'schools',
            'classes',
            'activities',
            'totalEnrolled',
            'checkedIn',
            'title',
            'checkedOut',
            'all_schools',
            'all_classes'
        ));
    }
    public function getAttendance(Request $request, $classId)
    {
        $date = $request->get('date', now()->format('Y-m-d'));

        $students = ProgramStudentEnrollment::where('program_id', $classId)
            ->with(['student', 'program.program_schedule'])
            ->get()
            ->map(function ($enrollment) use ($date) {
                $attendance = ProgramStudentAttendance::where("student_id", $enrollment->student_id)->where("program_id", $enrollment->program_id)->where("date", $date)->first();

                return [
                    'id' => $enrollment->student_id,
                    'name' => $enrollment->student->name,
                    'check_in' => $attendance?->check_in,
                    'check_out' => $attendance?->check_out,
                    'checked_in_by' => $attendance?->checkedInBy?->name,
                    'checked_out_by' => $attendance?->checkedOutBy?->name
                ];
            });

        $program = Program::with('program_schedule')->find($classId);
        $program->program_schedule->start_time = date("H:i", strtotime($program->program_schedule->start_time));
        $program->program_schedule->end_time = date("H:i", strtotime($program->program_schedule->end_time));

        return response()->json([
            'students' => $students,
            'program' => $program
        ]);
    }

    public function updateAttendance(Request $request, $classId)
    {
        $request->validate([
            'date' => 'required|date',
            'students' => 'required|array',
            'students.*.student_id' => 'required|exists:students,id',
            'students.*.check_in' => 'nullable|date_format:H:i',
            'students.*.check_out' => 'nullable|date_format:H:i|after:students.*.check_in'
        ]);

        DB::beginTransaction();
        try {
            foreach ($request->students as $student) {
                ProgramStudentAttendance::updateOrCreate(
                    [
                        'program_id' => $classId,
                        'student_id' => $student['student_id'],
                        'date' => $request->date
                    ],
                    [
                        'check_in' => $student['check_in'] ?? null,
                        'check_out' => $student['check_out'] ?? null,
                        'checked_in_by' => $student['check_in'] ? auth()->id() : null,
                        'checked_out_by' => $student['check_out'] ? auth()->id() : null
                    ]
                );
            }
            DB::commit();
            return response()->json(['message' => 'Attendance updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Error updating attendance'], 500);
        }
    }

    public function bulkAttendanceUpdate(Request $request, $classId)
    {
        $validated = $request->validate([
            'class_id' => 'required|exists:programs,id',
            'date' => 'required|date',
            'attendance' => 'required|array',
            'attendance.*.student_id' => 'required|exists:students,id',
            'attendance.*.check_in' => 'nullable',
            'attendance.*.check_out' => 'nullable',
            'attendance.*.checked_in_by' => 'nullable|string',
            'attendance.*.checked_out_by' => 'nullable|string',
        ]);
        DB::beginTransaction();
        try {
            // Get program schedule and lesson
            $program_schedule = ProgramSchedule::where("program_id", $classId)->first();
            $lesson = Lesson::where("lesson_date", $request->date)
                ->where("program_schedule_id", $program_schedule->id)
                ->first();

            if ($lesson) {
                foreach ($request->attendance as $attendance) {
                    $update = [
                        'program_id' => $classId,
                        'student_id' => $attendance['student_id'],
                        'date' => $request->date,
                        'lesson_id' => $lesson->id
                    ];

                    $data = [
                        'check_in' => $attendance['check_in'] ?? null,
                        'check_out' => $attendance['check_out'] ?? null,
                        'checked_in_by' => auth()->user()->id ?? null,
                        'checked_out_by' => auth()->user()->id ?? null
                    ];

                    ProgramStudentAttendance::updateOrCreate($update, $data);
                }

                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => 'Bulk attendance updated successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No lesson found for this date'
            ], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function classCatalogue(Request $request)
    {

        $blueprints = ClassBlueprint::where("provider_id", auth()->user()->id)->get();
        $title = "Class Catalogue";
        return view('pages.providers.class-catalogue', compact('blueprints', 'title'));
    }
    public function storeClass(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|exists:schools,id',
            'title' => 'required|string|max:255',
            'session' => 'required|string',
            'start_date' => 'required',
            'end_date' => 'required',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'fee' => 'required|numeric|min:0',

            'start_time' => 'required',
            'end_time' => 'required',
            'location' => 'required|string',
            'instructor_id' => 'required|exists:instructors,id',
            'enrollment_start' => 'required',
            'enrollment_end' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {

            $program = new Program();
            $program->service_provider_id = Auth::id();
            $program->school_id = $request->school_id;

            $program->title = $request->title;
            $program->session = $request->session;
            $program->description = $request->description;
            $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
            $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
            $program->min_students = $request->min_students;
            if ($request->no_class_dates) {
                $program->no_class_dates = implode(",", $request->no_class_dates);
            }

            $program->max_students = $request->max_students;
            $program->age_range = $request->age_range;
            $program->fee = $request->fee;
            $program->supply_cost = $request->supply_cost ?? 0;

            $program->location = $request->location;
            $program->enrollment_start = Carbon::createFromFormat("m-d-Y", ($request->enrollment_start))->format("Y-m-d");
            $program->enrollment_end = Carbon::createFromFormat("m-d-Y", ($request->enrollment_end))->format("Y-m-d");
            $program->registration_questions = $request->registration_questions;
            $program->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $program->status = 'Inactive';
            $program->save();
            if ($program) {
                if ($request->filled('instructor_id')) {
                    InstructorProgram::create([
                        'program_id' => $program->id,
                        'instructor_id' => $request->instructor_id,
                        'assigned_at' => now(),
                        'status' => 'Active'
                    ]);
                }

                $schedule = new ProgramSchedule();
                $schedule->start_time = $request->start_time;
                $schedule->end_time = $request->end_time;
                $schedule->program_id = $request->program_id;
                $schedule->days = implode(",", $request->days);
                $schedule->year = date("Y");
                $schedule->month = date("m");
                $schedule->program_id = $program->id;
                $schedule->save();
                // save blueprint
                if ($request->filled('blueprint_id') && ClassBlueprint::find($request->blueprint_id)) {
                    $blueprint = ClassBlueprint::find($request->blueprint_id);
                    $blueprint->usage_count = $blueprint->usage_count + 1;
                    $blueprint->save();
                } else {
                    $blueprint = new ClassBlueprint();
                    $blueprint->provider_id = Auth::id();
                    $blueprint->title = $request->title;
                    $blueprint->description = $request->description;
                    $blueprint->age_range = $request->age_range;
                    $blueprint->cost = $request->fee;
                    $blueprint->supply_cost = $request->supply_cost ?? 0;
                    $blueprint->min_students = $request->min_students;
                    $blueprint->max_students = $request->max_students;
                    $blueprint->day_of_week = implode(",", $request->days);
                    $blueprint->start_time = $request->start_time;
                    $blueprint->end_time = $request->end_time;
                    $blueprint->location = $request->location;
                    $blueprint->registration_questions = $request->registration_questions;
                    $blueprint->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
                    $blueprint->usage_count = 1;
                    $blueprint->save();
                    $program->blueprint_id = $blueprint->id;
                    $program->save();
                }
            }
            return response()->json([
                'success' => true,
                'message' => 'Class created successfully',
                'redirect' => true,
                'route' => route('service_provider.class_details', $program->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }
    public function saveDraft(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|exists:schools,id',
            'title' => 'required|string|max:255',
            'session' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'day_of_week' => 'required|string',
            'start_time' => 'required',
            'end_time' => 'required',
            'location' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'enrollment_start' => 'required|date',
            'enrollment_end' => 'required|date|after_or_equal:enrollment_start',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $draft = new ClassDraft();
            $draft->provider_id = Auth::id();
            $draft->school_id = $request->school_id;
            $draft->title = $request->title;
            $draft->session = $request->session;
            $draft->description = $request->description;
            $draft->start_date = $request->start_date;
            $draft->end_date = $request->end_date;
            $draft->no_class_dates = $request->no_class_dates;
            $draft->min_students = $request->min_students;
            $draft->max_students = $request->max_students;
            $draft->age_range = $request->age_range;
            $draft->cost = $request->cost;
            $draft->supply_cost = $request->supply_cost ?? 0;
            $draft->day_of_week = $request->day_of_week;
            $draft->start_time = $request->start_time;
            $draft->end_time = $request->end_time;
            $draft->location = $request->location;
            $draft->instructor_id = $request->instructor_id;
            $draft->enrollment_start = $request->enrollment_start;
            $draft->enrollment_end = $request->enrollment_end;
            $draft->registration_questions = $request->registration_questions;
            $draft->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $draft->is_draft =  1;

            $draft->save();

            return response()->json([
                'success' => true,
                'message' => 'Draft saved successfully',
                'redirect' => true,
                'route' => route('service_provider.classes')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
    public function createClass()
    {
        $instructors = Instructor::where('service_provider_id', auth()->user()->id)->get();
        $blueprints = ClassBlueprint::where('provider_id', auth()->user()->id)->get();
        $sessions = Session::where("school_id", auth()->user()->school_id)->get();
        $title = "Create Class";

        return view('pages.providers.classes.create', compact('instructors', 'blueprints', 'sessions', 'title'));
    }
    public function editClass($id)
    {
        $program = Program::findOrFail($id);
        $instructors = Instructor::where('service_provider_id', auth()->user()->id)->get();
        $blueprints = ClassBlueprint::where('provider_id', auth()->user()->id)->get();
        $sessions = Session::where("school_id", auth()->user()->school_id)->get();
        $programInstructor = InstructorProgram::where("program_id", $program->id)->first();

        $title = "Edit Draft";
        return view('pages.providers.classes.create', compact('program', 'instructors', 'blueprints', 'sessions', 'title', 'programInstructor'));
    }
    public function getBlueprint()
    {
        $blueprint = ClassBlueprint::find(request('blueprint_id'));
        return response()->json(['data' =>  $blueprint, 'success' => true]);
    }
    public function deleteClass($id)
    {
        $program = Program::find($id);
        if ($program) {
            $program->delete();
            return response()->json(['message' => 'Class deleted successfully', 'success' => true, 'status' => 200]);
        }
        return response()->json(['message' => 'Class does not exists', 'success' => false, 'status' => 200]);
    }
    public function updateClass(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'school_id' => 'required|exists:schools,id',
            'title' => 'required|string|max:255',
            'session' => 'required|string',
            'start_date' => 'required',
            'end_date' => 'required',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'fee' => 'required|numeric|min:0',
            'start_time' => 'required',
            'end_time' => 'required',
            'location' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'enrollment_start' => 'required',
            'enrollment_end' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $program = Program::find($id);
            $program->title = $request->title;
            $program->session = $request->session;
            $program->description = $request->description;
            $program->start_date = Carbon::createFromFormat("m-d-Y", ($request->start_date))->format("Y-m-d");
            $program->end_date = Carbon::createFromFormat("m-d-Y", ($request->end_date))->format("Y-m-d");
            $program->no_class_dates = implode(",", $request->no_class_dates);
            $program->min_students = $request->min_students;
            $program->max_students = $request->max_students;
            $program->age_range = $request->age_range;
            $program->fee = $request->fee;
            $program->supply_cost = $request->supply_cost ?? 0;


            $program->location = $request->location;
            $program->enrollment_start = Carbon::createFromFormat("m-d-Y", ($request->enrollment_start))->format("Y-m-d");
            $program->enrollment_end = Carbon::createFromFormat("m-d-Y", ($request->enrollment_end))->format("Y-m-d");
            $program->registration_questions = $request->registration_questions;
            $program->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $program->status = 'upcoming';
            $program->updated_by = auth()->id();
            $program->updated_by_type = "provider";

            $program->updated_at = now();
            $program->save();
            if ($program) {
                if ($request->filled('instructor_id')) {
                    InstructorProgram::updateOrCreate(
                        ['program_id' => $program->id],
                        [
                            'instructor_id' => (int) $request->instructor_id,
                            'assigned_at' => now(),
                            'status' => 'Active'
                        ]
                    );
                } else {
                    InstructorProgram::where('program_id', $program->id)->delete();
                }


                $schedule =  ProgramSchedule::find($program->program_schedule->id);


                $schedule->start_time = $request->start_time;
                $schedule->end_time = $request->end_time;
                $schedule->program_id = $request->program_id;
                $schedule->days = implode(",", $request->days);
                $schedule->year = date("Y");
                $schedule->month = date("m");
                $schedule->program_id = $program->id;

                $schedule->save();
            }
            return response()->json([
                'success' => true,
                'message' => 'Class updated successfully',
                'redirect' => true,
                'route' => route('service_provider.class_details', $program->id)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
    // my schools
    public function mySchools()
    {
        // Get schools associated with this provider
        $schools = SchoolProvider::where('service_provider_id', auth()->user()->id)
            ->pluck("school_id")
            ->toArray();
        if (empty($school)) {
            $schools[] = auth()->user()->school_id;
        }
        // Get school details with related data and counts
        $schools = School::whereIn("id", $schools)
            ->with([
                'programs' => function ($q) {
                    $q->where('service_provider_id', auth()->user()->id);
                },
                'sessions' => function ($q) {
                    $q->where('status', 'active');
                }
            ])
            ->when(request()->has('search'), function ($query) {
                return $query->where('name', 'LIKE', '%' . request('search') . '%');
            })
            ->withCount([
                'programs as active_classes_count' => function ($q) {
                    $q->where('service_provider_id', auth()->user()->id)
                        ->where('status', 'Active');
                },
                'programs as total_students_count' => function ($q) {
                    $q->where('service_provider_id', auth()->user()->id)
                        ->withCount('enrollments');
                },

            ])
            ->get();

        // Add additional data for each school
        foreach ($schools as $school) {
            // Calculate total earnings
            $school->total_earnings = $school->programs->sum(function ($program) {
                return $program->enrollments->count() * $program->fee;
            });

            // Get latest activity
            $school->latest_activity = Program::where('school_id', $school->id)
                ->where('service_provider_id', auth()->user()->id)
                ->orderBy('updated_at', 'desc')
                ->first();

            // Set compliance status
            $school->compliance_status = 0;
            $school->pending_compliance_count = 0;
        }

        $title = "My Schools";

        // Get summary statistics  
        $summary = [
            'total_schools' => $schools->count(),
            'total_programs' => $schools->sum('active_classes_count'),
            'total_students' => $schools->sum('total_students_count'),
            'total_earnings' => $schools->sum('total_earnings')
        ];


        return view('pages.providers.schools.index', compact(
            'schools',
            'title',
            'summary'
        ));
    }
    public function network()
    {
        // Get schools associated with this provider
        $schools = SchoolProvider::where('service_provider_id', auth()->user()->id)
            ->pluck("school_id")
            ->toArray();
        if (empty($school)) {
            $schools[] = auth()->user()->school_id;
        }
        // Get school details with related data and counts
        $schools = School::with([
            'programs' => function ($q) {
                $q->where('service_provider_id', auth()->user()->id);
            },
            'sessions' => function ($q) {
                $q->where('status', 'active');
            }
        ])
            ->when(request()->has('search'), function ($query) {
                return $query->where('name', 'LIKE', '%' . request('search') . '%');
            })
            ->withCount([
                'programs as active_classes_count' => function ($q) {
                    $q->where('service_provider_id', auth()->user()->id)
                        ->where('status', 'Active');
                },
                'programs as total_students_count' => function ($q) {
                    $q->where('service_provider_id', auth()->user()->id)
                        ->withCount('enrollments');
                },

            ])
            ->get();


        $title = "Network";



        return view('pages.providers.schools.network', compact(
            'schools',
            'title',

        ));
    }

    public function schoolHistory($id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }

        $school = School::find($id);
        $title = "School History";

        // Get all classes and group by year
        $classes = Program::where("school_id", $id)
            ->where("service_provider_id", auth()->user()->id)
            ->withCount('enrollments as enrolled_students_count')
            ->get()
            ->groupBy(function ($class) {
                return date('Y', strtotime($class->start_date));
            })
            ->sortKeysDesc();

        return view('pages.providers.schools.history', compact('school', 'title', 'classes'));
    }

    public function schoolCompliance($id)
    {
        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $school = School::find($id);
        $title = "School Compliance";
        // ProviderRequirement::where("service_provider_id", auth()->user()->id)->get();

        return view('pages.providers.schools.compliance', compact('school', 'title'));
    }
    // my school details
    public function mySchoolDetails($id)
    {

        if (strlen($id) > 4) {
            $id = decrypt($id);
        }
        $school = School::find($id);
        $programs = Program::where("school_id", $id)->where("service_provider_id", auth()->user()->id)->get();
        // $compliance=SchoolRequirement::where("school_id",$id)->where("service_provider_id",auth()->user()->id)->get();
        $title = "School Details";
        $schoolPolicies = \App\Models\Policy::where('school_id', $id)->where('type', 'school')->get();
        return view('pages.providers.schools.details', compact('school', 'title', 'programs', 'schoolPolicies'));
    }

    public function reports(Request $request)
    {
        $provider_id = auth()->user()->id;

        // Get all transactions/earnings data with filters
        $earnings = Program::where('service_provider_id', $provider_id)
            ->with(['school', 'program_schedule', 'enrollments'])
            ->when($request->filled('school_id'), function ($q) use ($request) {
                return $q->where('school_id', $request->school_id);
            })
            ->when($request->filled('program_id'), function ($q) use ($request) {
                return $q->where('id', $request->program_id);
            })
            ->when($request->filled('year'), function ($q) use ($request) {
                return $q->whereYear('start_date', $request->year);
            })
            ->when($request->filled('grade'), function ($q) use ($request) {
                return $q->whereHas('enrollments.student', function ($query) use ($request) {
                    $query->where('grade', $request->grade);
                });
            })
            ->get();

        // Calculate summary statistics
        $summary = [
            'total_earnings' => $earnings->sum(function ($program) {
                return $program->enrollments->count() * $program->fee;
            }),
            'total_programs' => $earnings->count(),
            'total_students' => $earnings->sum(function ($program) {
                return $program->enrollments->count();
            }),
        ];

        // Get filter options
        $schools = School::whereHas('programs', function ($q) use ($provider_id) {
            $q->where('service_provider_id', $provider_id);
        })->get();

        $programs = Program::where('service_provider_id', $provider_id)->get();

        $years = Program::where('service_provider_id', $provider_id)
            ->selectRaw('YEAR(start_date) as year')
            ->distinct()
            ->pluck('year');

        $grades = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
        $title = "Reports";
        return view('pages.providers.reports', compact(
            'earnings',
            'summary',
            'schools',
            'programs',
            'years',
            'grades',
            'title'
        ));
    }
    public function searchStudents(Request $request)
    {
        $query = $request->input('query');
        $school_ids = SchoolProvider::where("service_provider_id", auth()->user()->id)->pluck("school_id")->toArray();
        $school_ids = ServiceProvider::where("id", Auth::id())->pluck("school_id")->toArray();
        $schools = School::where('name', 'LIKE', "%$query%")
            ->whereIn("id", $school_ids)
            ->get();
        $programs = Program::where('id', 'LIKE', "%$query%")
            ->where('service_provider_id', auth()->user()->id)
            ->orWhere('title', 'LIKE', "%$query%")
            ->with('enrollments.student')
            ->get();
        $student_ids = ProgramStudentEnrollment::where("program_id", $programs->pluck("id")->toArray())->pluck("student_id")->toArray();
        $students = Student::where('name', 'LIKE', "%$query%")
            ->whereIn("id", $student_ids)
            ->get();

        return response()->json(['students' => $students, 'schools' => $schools, 'programs' => $programs]);
    }

    // catalogues functions
    public function newClassCatalogue()
    {
        $blueprints = ClassBlueprint::where("provider_id", auth()->user()->id)->get();
        $title = "Class Catalogue";
        return view('pages.providers.new-class-catalogue', compact('blueprints', 'title'));
    }
    public function createClassCatalogue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'supply_cost' => 'nullable|numeric|min:0',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',

            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
            'location' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $blueprint = new ClassBlueprint();
            $blueprint->provider_id = Auth::id();
            $blueprint->title = $request->title;
            $blueprint->description = $request->description;
            $blueprint->age_range = $request->age_range;
            $blueprint->cost = $request->cost;
            $blueprint->supply_cost = $request->supply_cost ?? 0;
            $blueprint->min_students = $request->min_students;
            $blueprint->max_students = $request->max_students;
            $blueprint->day_of_week = implode(",", $request->days);
            $blueprint->start_time = $request->start_time;
            $blueprint->end_time = $request->end_time;
            $blueprint->location = $request->location;
            $blueprint->registration_questions = $request->registration_questions;
            $blueprint->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $blueprint->usage_count = 0;
            $blueprint->save();

            return response()->json([
                'success' => true,
                'message' => 'Blueprint created successfully',
                'redirect' => true,
                'route' => route('service_provider.class.catalogue')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
    public function editClassCatalogue($id)
    {
        $blueprint = ClassBlueprint::findOrFail($id);
        $title = "Edit Blueprint";
        return view('pages.providers.new-class-catalogue', compact('blueprint', 'title'));
    }
    public function updateClassCatalogue(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'age_range' => 'required|string',
            'cost' => 'required|numeric|min:0',
            'supply_cost' => 'nullable|numeric|min:0',
            'min_students' => 'required|integer|min:1',
            'max_students' => 'required|integer|gte:min_students',

            'start_time' => 'required',
            'end_time' => 'required|after:start_time',
            'location' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ]);
        }

        try {
            $blueprint = ClassBlueprint::findOrFail($id);
            $blueprint->title = $request->title;
            $blueprint->description = $request->description;
            $blueprint->age_range = $request->age_range;
            $blueprint->cost = $request->cost;
            $blueprint->supply_cost = $request->supply_cost ?? 0;
            $blueprint->min_students = $request->min_students;
            $blueprint->max_students = $request->max_students;
            $blueprint->day_of_week = implode(",", $request->days);
            $blueprint->start_time = $request->start_time;
            $blueprint->end_time = $request->end_time;
            $blueprint->location = $request->location;
            $blueprint->registration_questions = $request->registration_questions;
            $blueprint->enable_checkin = $request->has('enable_checkin') ? 1 : 0;
            $blueprint->save();

            return response()->json([
                'success' => true,
                'message' => 'Blueprint updated successfully',
                'redirect' => true,
                'route' => route('service_provider.class.catalogue')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
    public function deleteClassCatalogue($id)
    {
        try {
            $blueprint = ClassBlueprint::findOrFail($id);
            $blueprint->delete();

            return response()->json([
                'success' => true,
                'message' => 'Blueprint deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }
}
