<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Program extends Model
{
    use HasFactory;
    protected $fillable = [
        'title',
        'school_id',
        'image',
        'description',
        'fee',
        'commission',
        'status',
        'session',
        'service_provider_id',
        'start_date',
        'end_date',
        'status',
        'created_by',
        'created_by_type',
    ];

    public function program_schedule()
    {
        return $this->hasOne(ProgramSchedule::class);
    }
    public function provider()
    {
        return $this->belongsTo(ServiceProvider::class, 'service_provider_id');
    }
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id');
    }


    public function TotalKidEnrolled()
    {
        return  ProgramStudentEnrollment::where("program_id", $this->id)->count();
    }
    public function instructors()
    {
        return $this->belongsToMany(Instructor::class, 'instructor_programs')
                    ->withPivot('assigned_at', 'status', 'notes')
                    ->withTimestamps();
    }
    public function primaryInstructor()
    {
        return $this->instructors()->wherePivot('status', 'Active')->first();
    }
    public function instructor()
    {
        return $this->belongsTo(Instructor::class, 'instructor_id');
    }
    public function enrollments()
    {
        return $this->hasMany(ProgramStudentEnrollment::class, 'program_id');
    }
     public function attendances(){
        return $this->hasMany(ProgramStudentAttendance::class, 'program_id');
    }
      public function lessons(){
        return $this->hasMany(Lesson::class, 'program_id');
    }
    public function creator()
    {
        return $this->morphTo('created_by');
    }
    public function createLessons()
    {
        $schedule = $this->program_schedule;
        if ($schedule) {
            $days = explode(',', $schedule->days);
            $start_date = $schedule->start_date;
            $end_date = $schedule->end_date;
            $start_time = $schedule->start_time;
            $end_time = $schedule->end_time;
            $duration = $schedule->duration;
            $currentDate = Carbon::parse($start_date);
            $endDate = Carbon::parse($end_date);
            $lessonNumber = 1; // Start lesson numbering
            while ($currentDate->lte($endDate)) {
                if (in_array($currentDate->format('l'), $days)) {
                    $check=Lesson::where("program_schedule_id", $schedule->id)->where("lesson_date", $currentDate->toDateString())->first();
                    if($check){
                        continue;
                    }
                    DB::table('lessons')->insert([
                        'program_schedule_id' => $schedule->id,
                        'program_id' => $this->id,
                        'title' => 'Lesson ' . $lessonNumber,
                        'description' => 'Lesson ' . $lessonNumber . ' for ' . $this->title,
                        'instructor_id' => $this->instructor_id,
                        'service_provider_id' => $this->service_provider_id,
                        'lesson_date' => $currentDate->toDateString(),
                        'start_time' => $start_time,
                        'end_time' => $end_time,
                        'shift' => $this->getShift($start_time),
                        'lesson_number' => $lessonNumber,
                        'status' => 'Scheduled',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    $lessonNumber++; // Increment for next lesson
                }
                $currentDate->addDay();
            }
        }
    }
}
